from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.responses import J<PERSON>NResponse
import asyncio
import time
import json
import uuid
from typing import Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)  # 将日志级别改为DEBUG以获取更多信息
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(prefix="/api/ws", tags=["websocket"])

# 会话存储
sessions: Dict[str, Dict[str, Any]] = {}

# WebSocket连接管理器
class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.lock = asyncio.Lock()
        logger.debug("WebSocket管理器已初始化")
        print("[WebSocket] 管理器已初始化")
    
    async def connect(self, session_id: str, websocket: WebSocket):
        """添加新的WebSocket连接"""
        async with self.lock:
            try:
                logger.info(f"尝试建立WebSocket连接: {session_id}, 连接对象ID: {id(websocket)}")
                print(f"[WebSocket] 尝试建立连接: {session_id}")
                await websocket.accept()
                self.active_connections[session_id] = websocket
                logger.info(f"WebSocket连接已建立: {session_id}, 当前活跃连接数: {len(self.active_connections)}")
                print(f"[WebSocket] 连接已建立: {session_id}, 当前活跃连接数: {len(self.active_connections)}")
                
                # 在连接建立后发送一条欢迎消息
                await websocket.send_json({
                    "event_type": "system",
                    "data": {
                        "content": f"WebSocket连接已建立 - 会话ID: {session_id}",
                        "is_final": False
                    }
                })
                
                # 发送一条调试状态消息
                await websocket.send_json({
                    "event_type": "debug",
                    "data": {
                        "content": f"当前会话状态: {sessions.get(session_id, {'status': 'unknown'})}",
                        "is_final": False
                    }
                })
                
                print(f"[WebSocket] 已发送欢迎消息: {session_id}")
            except Exception as e:
                logger.error(f"WebSocket连接建立失败: {session_id}, 错误: {str(e)}")
                print(f"[WebSocket错误] 连接建立失败: {session_id}, 错误: {str(e)}")
                raise
    
    async def disconnect(self, session_id: str):
        """移除WebSocket连接"""
        async with self.lock:
            if session_id in self.active_connections:
                del self.active_connections[session_id]
                logger.info(f"WebSocket连接已关闭: {session_id}, 当前活跃连接数: {len(self.active_connections)}")
                print(f"[WebSocket] 连接已关闭: {session_id}, 当前活跃连接数: {len(self.active_connections)}")
            else:
                logger.warning(f"尝试关闭不存在的WebSocket连接: {session_id}")
                print(f"[WebSocket警告] 尝试关闭不存在的连接: {session_id}")
    
    async def send_message(self, session_id: str, message: Dict[str, Any]):
        """向指定会话发送消息"""
        if session_id in self.active_connections:
            try:
                # 日志记录发送的消息（但避免记录过长的内容）
                msg_summary = str(message)
                if len(msg_summary) > 200:
                    event_type = message.get("event_type", "unknown")
                    data_preview = message.get("data", {})
                    if isinstance(data_preview, dict) and "content" in data_preview:
                        content_preview = data_preview["content"]
                        if isinstance(content_preview, str) and len(content_preview) > 100:
                            data_preview["content"] = content_preview[:100] + "..."
                    msg_summary = f"类型:{event_type}, 数据:{data_preview}"
                
                logger.info(f"向会话 {session_id} 发送消息: {msg_summary}")
                print(f"[WebSocket发送] 会话: {session_id}, 类型: {message.get('event_type', 'unknown')}")
                await self.active_connections[session_id].send_json(message)
                
                # 更新会话活动时间
                if session_id in sessions:
                    sessions[session_id]["last_activity"] = time.time()
                
                return True
            except Exception as e:
                logger.error(f"向会话 {session_id} 发送消息失败: {str(e)}")
                print(f"[WebSocket错误] 发送消息失败: {session_id}, 错误: {str(e)}")
                # 如果发送失败，可能是连接已断开
                try:
                    await self.disconnect(session_id)
                except Exception as disconnect_error:
                    logger.error(f"断开失败的连接时出错: {str(disconnect_error)}")
                    print(f"[WebSocket错误] 断开失败的连接时出错: {session_id}, 错误: {str(disconnect_error)}")
                return False
        else:
            logger.warning(f"尝试向不存在的会话 {session_id} 发送消息")
            print(f"[WebSocket警告] 尝试向不存在的会话发送消息: {session_id}")
            return False
    
    async def broadcast(self, message: Dict[str, Any]):
        """向所有活跃连接广播消息"""
        for session_id in list(self.active_connections.keys()):
            await self.send_message(session_id, message)
    
    def is_connected(self, session_id: str) -> bool:
        """检查会话是否连接"""
        return session_id in self.active_connections
    
    def get_active_connections_count(self) -> int:
        """获取当前活跃连接数"""
        return len(self.active_connections)

# 创建WebSocket管理器实例
websocket_manager = WebSocketManager()

# 任务管理器 - 负责管理与会话关联的代理任务
class AgentTaskManager:
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.lock = asyncio.Lock()
    
    async def start_task(self, session_id: str, prompt: str):
        """启动与会话关联的Agent任务"""
        async with self.lock:
            # 创建可取消的任务
            task = asyncio.create_task(self._run_agent(session_id, prompt))
            self.tasks[session_id] = {
                "task": task,
                "status": "running",
                "start_time": time.time(),
                "prompt": prompt
            }
            logger.info(f"已启动Agent任务: {session_id}")
            return session_id
    
    async def stop_task(self, session_id: str):
        """安全停止任务"""
        async with self.lock:
            if session_id in self.tasks and self.tasks[session_id]["status"] == "running":
                # 更新状态
                self.tasks[session_id]["status"] = "stopping"
                
                try:
                    # 取消任务
                    task = self.tasks[session_id]["task"]
                    task.cancel()
                    
                    # 等待任务完成取消（有一个短暂的超时）
                    try:
                        await asyncio.wait_for(task, timeout=2.0)
                    except asyncio.TimeoutError:
                        logger.warning(f"任务 {session_id} 未能在超时内完成取消")
                    except asyncio.CancelledError:
                        logger.info(f"任务 {session_id} 已成功取消")
                    
                    self.tasks[session_id]["status"] = "stopped"
                    logger.info(f"已停止任务 {session_id}")
                except Exception as e:
                    logger.error(f"停止任务 {session_id} 时出错: {e}")
                    # 即使出错，我们也将状态标记为error
                    self.tasks[session_id]["status"] = "error"
    
    async def _run_agent(self, session_id: str, prompt: str):
        """运行Agent的包装函数，包含异常处理"""
        try:
            # 导入必要的AutoGen库（延迟导入以避免循环依赖）
            # 注意：实际实现时需要替换为真实的AutoGen导入
            # from app.agent.enhancer import run_prompt_enhancement
            
            # 模拟Agent处理逻辑
            # 发送思考事件
            await websocket_manager.send_message(
                session_id,
                {
                    "event_type": "thinking",
                    "data": {
                        "content": "正在分析提示词...",
                        "is_final": False
                    }
                }
            )
            
            # 等待一段时间，模拟处理过程
            await asyncio.sleep(1)
            
            # 这里应该调用实际的AutoGen代理处理
            # await run_prompt_enhancement(prompt, session_id, websocket_manager)
            
            # 为了示例，我们发送一个模拟的最终结果
            await websocket_manager.send_message(
                session_id,
                {
                    "event_type": "content",
                    "data": {
                        "content": f"增强后的提示词: {prompt} + 更多细节和创意元素",
                        "is_final": True
                    }
                }
            )
            
            return "任务完成"
        except asyncio.CancelledError:
            # 正常取消，清理资源
            logger.info(f"Agent任务 {session_id} 被取消")
            # 通知前端任务已取消
            try:
                await websocket_manager.send_message(
                    session_id,
                    {
                        "event_type": "error",
                        "data": {
                            "message": "任务已被取消",
                            "is_final": True
                        }
                    }
                )
            except:
                pass
            raise  # 重新抛出以便调用者知道任务被取消
        except Exception as e:
            # 捕获所有其他异常
            logger.error(f"Agent任务 {session_id} 执行出错: {e}")
            # 记录详细错误信息供调试
            import traceback
            traceback.print_exc()
            # 尝试通知前端（如果WebSocket还在）
            try:
                await websocket_manager.send_message(
                    session_id,
                    {
                        "event_type": "error",
                        "data": {
                            "message": f"处理过程中出错: {str(e)}",
                            "is_final": True
                        }
                    }
                )
            except:
                # 忽略发送错误的错误
                pass
            return None

# 创建任务管理器实例
task_manager = AgentTaskManager()

# 辅助函数: 验证会话
async def validate_session(session_id: str) -> bool:
    """验证会话ID是否有效"""
    return session_id in sessions

# WebSocket连接端点
@router.websocket("/enhance/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    logger.info(f"收到WebSocket连接请求: {session_id}")
    print(f"[WebSocket] 收到连接请求: {session_id}")
    
    # 验证会话ID
    if not await validate_session(session_id):
        logger.warning(f"拒绝无效会话ID的WebSocket连接: {session_id}")
        print(f"[WebSocket错误] 无效的会话ID: {session_id}")
        await websocket.close(code=1008, reason="无效的会话ID")
        return
    
    # 建立连接
    try:
        logger.info(f"正在建立WebSocket连接: {session_id}")
        print(f"[WebSocket] 正在建立连接: {session_id}")
        await websocket_manager.connect(session_id, websocket)
        
        # 更新会话状态
        sessions[session_id]["status"] = "connected"
        sessions[session_id]["last_activity"] = time.time()
        logger.info(f"会话状态已更新: {session_id} -> connected")
        print(f"[WebSocket] 会话状态已更新: {session_id} -> connected")
        
        # 发送欢迎消息
        await websocket_manager.send_message(
            session_id,
            {
                "event_type": "system",
                "data": {
                    "content": "WebSocket连接已建立，准备接收提示词增强结果",
                    "is_final": False
                }
            }
        )
        
        # 主消息循环
        try:
            while True:
                # 接收消息
                try:
                    data = await websocket.receive_text()
                    logger.debug(f"收到WebSocket消息: {session_id}, 数据: {data[:100]}..." if len(data) > 100 else f"收到WebSocket消息: {session_id}, 数据: {data}")
                    print(f"[WebSocket] 收到消息: {session_id}, 数据: {data[:100]}..." if len(data) > 100 else f"[WebSocket] 收到消息: {session_id}, 数据: {data}")
                    
                    # 解析JSON消息
                    try:
                        message = json.loads(data)
                        
                        # 处理心跳消息
                        if message.get("type") == "heartbeat":
                            # 更新活动时间
                            sessions[session_id]["last_activity"] = time.time()
                            await websocket.send_json({"type": "heartbeat_ack"})
                            continue
                        
                        # 处理取消消息
                        if message.get("type") == "cancel":
                            logger.info(f"收到取消请求: {session_id}")
                            print(f"[WebSocket] 收到取消请求: {session_id}")
                            await task_manager.stop_task(session_id)
                            
                            # 发送取消确认
                            await websocket_manager.send_message(
                                session_id,
                                {
                                    "event_type": "system",
                                    "data": {
                                        "content": "任务已取消",
                                        "is_final": True
                                    }
                                }
                            )
                            continue
                        
                        # 其他类型的消息
                        logger.info(f"收到其他类型的消息: {session_id}, 类型: {message.get('type', 'unknown')}")
                        print(f"[WebSocket] 收到其他类型的消息: {session_id}, 类型: {message.get('type', 'unknown')}")
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"无法解析JSON消息: {session_id}, 错误: {str(e)}")
                        print(f"[WebSocket警告] 无法解析JSON消息: {session_id}, 错误: {str(e)}")
                        
                except Exception as e:
                    logger.error(f"接收WebSocket消息时出错: {session_id}, 错误: {str(e)}")
                    print(f"[WebSocket错误] 接收消息时出错: {session_id}, 错误: {str(e)}")
                    # 可能连接已断开，退出循环
                    break
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket已断开连接: {session_id}")
            print(f"[WebSocket] 连接已断开: {session_id}")
            await websocket_manager.disconnect(session_id)
            
    except Exception as e:
        logger.error(f"处理WebSocket连接时出错: {session_id}, 错误: {str(e)}")
        print(f"[WebSocket错误] 处理连接时出错: {session_id}, 错误: {str(e)}")
        # 确保连接已关闭
        try:
            await websocket_manager.disconnect(session_id)
        except:
            pass

# 创建会话API端点
@router.post("/enhance/session", response_class=JSONResponse)
async def create_session():
    """创建新的会话"""
    session_id = str(uuid.uuid4())
    sessions[session_id] = {
        "id": session_id,
        "created_at": time.time(),
        "status": "created",
        "last_activity": time.time()
    }
    logger.info(f"创建新会话: {session_id}")
    return {"session_id": session_id, "status": "created"}

# 会话清理API端点
@router.delete("/enhance/session/{session_id}", response_class=JSONResponse)
async def delete_session(session_id: str):
    """删除会话"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 停止相关任务
    await task_manager.stop_task(session_id)
    
    # 关闭WebSocket连接
    if websocket_manager.is_connected(session_id):
        await websocket_manager.disconnect(session_id)
    
    # 删除会话
    del sessions[session_id]
    logger.info(f"删除会话: {session_id}")
    
    return {"status": "success", "message": "会话已删除"}

# 会话状态API端点
@router.get("/enhance/session/{session_id}", response_class=JSONResponse)
async def get_session(session_id: str):
    """获取会话状态"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    session_info = sessions[session_id].copy()
    # 移除不应该暴露的信息
    if "error" in session_info:
        session_info["has_error"] = True
        del session_info["error"]
    
    return session_info

# 定期清理过期会话的任务
async def cleanup_sessions():
    """定期清理过期会话"""
    while True:
        try:
            now = time.time()
            session_ids_to_cleanup = []
            
            # 查找过期会话
            for session_id, session in sessions.items():
                # 断开连接超过30分钟的会话
                if session["status"] == "disconnected" and now - session.get("disconnected_at", 0) > 30 * 60:
                    session_ids_to_cleanup.append(session_id)
                # 不活跃超过60分钟的会话
                elif now - session.get("last_activity", 0) > 60 * 60:
                    session_ids_to_cleanup.append(session_id)
            
            # 清理过期会话
            for session_id in session_ids_to_cleanup:
                await task_manager.stop_task(session_id)
                if websocket_manager.is_connected(session_id):
                    await websocket_manager.disconnect(session_id)
                del sessions[session_id]
                logger.info(f"清理过期会话: {session_id}")
            
            # 等待下一次检查
            await asyncio.sleep(10 * 60)  # 每10分钟检查一次
        
        except Exception as e:
            logger.error(f"清理会话时出错: {e}")
            await asyncio.sleep(60)  # 出错后等待1分钟再试

# 启动后台任务
@router.on_event("startup")
async def startup_event():
    """应用启动时调用"""
    # 启动会话清理任务
    asyncio.create_task(cleanup_sessions())
    logger.info("WebSocket服务已启动，会话清理任务已创建")

# 关闭时的清理
@router.on_event("shutdown")
async def shutdown_event():
    """应用关闭时调用"""
    # 关闭所有WebSocket连接
    for session_id in list(websocket_manager.active_connections.keys()):
        try:
            await websocket_manager.disconnect(session_id)
        except:
            pass
    
    # 停止所有任务
    for session_id in list(task_manager.tasks.keys()):
        try:
            await task_manager.stop_task(session_id)
        except:
            pass
    
    logger.info("WebSocket服务已关闭，所有连接和任务已清理")