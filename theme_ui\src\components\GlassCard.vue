<template>
  <div class="glass-card" :class="['glass', `color-${color}`]">
    <div class="card-content">
      <div class="icon-container mb-4" v-if="$slots.icon">
        <slot name="icon"></slot>
      </div>
      <h3 v-if="title" class="card-title mb-2">{{ title }}</h3>
      <p v-if="description" class="card-description mb-4">{{ description }}</p>
      <div class="buttons-container" v-if="buttonText || buttonText_list">
        <button v-if="buttonText" class="card-button" :class="`btn-${color}`" @click="handleClick">
          {{ buttonText }}
        </button>
        <button v-if="buttonText_list" class="card-button secondary-button" :class="`btn-${color}-secondary`" @click="handleClick_list">
          {{ buttonText_list }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "GlassCard",
});
</script>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    default: "",
  },
  // 卡片描述
  description: {
    type: String,
    default: "",
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: "",
  },
  // 按钮文本
  buttonText_list: {
    type: String,
    default: "",
  },
  // 颜色主题
  color: {
    type: String,
    default: "primary",
    validator: (value: string) =>
      ["primary", "secondary", "info", "success", "warning", "danger"].includes(
        value
      ),
  },
  // 宽度，可以是CSS的宽度值
  width: {
    type: String,
    default: "100%",
  },
  // 高度，可以是CSS的高度值
  height: {
    type: String,
    default: "auto",
  },
  // 是否按照图片比例来调整卡片大小
  aspectRatio: {
    type: Boolean,
    default: false,
  },
  // 宽高比，用于aspectRatio为true时
  ratio: {
    type: Number,
    default: 16 / 9,
  },
  // 点击事件处理
  onClick: {
    type: Function,
    default: null,
  },
});

const emit = defineEmits(["click", "click_list"]);

// 计算卡片样式
const cardStyle = computed(() => {
  const style: Record<string, string> = {
    width: props.width,
    height: props.height,
  };

  if (props.aspectRatio && props.ratio) {
    style.paddingBottom = `${(1 / props.ratio) * 100}%`;
    style.height = "0";
  }

  return style;
});

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (props.onClick) {
    props.onClick(event);
  }
  emit("click", event);
};
const handleClick_list = (event: MouseEvent) => {
  if (props.onClick) {
    props.onClick(event);
  }
  emit("click_list", event);
};
</script>

<style scoped>
.glass-card {
  position: relative;
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
  padding: 24px;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(30, 41, 59, 0.7);
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.icon-container {
  margin-bottom: 16px;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.card-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16px;
  flex-grow: 1;
}

.card-button {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  color: white;
  flex: 1;
}

.buttons-container {
  display: flex;
  gap: 10px;
  width: 100%;
}

.secondary-button {
  background: rgba(99, 102, 241, 0.15) !important;
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

/* 颜色主题样式 */
.color-primary .card-title {
  color: #6366f1;
}

.color-secondary .card-title {
  color: #8b5cf6;
}

.color-info .card-title {
  color: #06b6d4;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.btn-secondary {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.btn-info {
  background: linear-gradient(135deg, #06b6d4, #0284c7);
}

.btn-primary:hover,
.btn-secondary:hover,
.btn-info:hover,
.btn-primary-secondary:hover,
.btn-secondary-secondary:hover,
.btn-info-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary-secondary {
  background: rgba(99, 102, 241, 0.2);
  color: #e0e7ff;
  border: 1px solid rgba(99, 102, 241, 0.5);
}

.btn-secondary-secondary {
  background: rgba(139, 92, 246, 0.2);
  color: #ede9fe;
  border: 1px solid rgba(139, 92, 246, 0.5);
}

.btn-info-secondary {
  background: rgba(6, 182, 212, 0.2);
  color: #cffafe;
  border: 1px solid rgba(6, 182, 212, 0.5);
}

/* SVG图标动画效果 */
.icon-svg {
  transition: transform 0.3s ease;
}

.glass-card:hover .icon-svg {
  transform: scale(1.1);
}

.icon-path,
.icon-path-delay1,
.icon-path-delay2,
.icon-border {
  stroke-dasharray: 100;
  stroke-dashoffset: 0;
  transition: all 0.5s ease;
}

.glass-card:hover .icon-path {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease forwards;
}

.glass-card:hover .icon-path-delay1 {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease 0.2s forwards;
}

.glass-card:hover .icon-path-delay2 {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease 0.4s forwards;
}

.glass-card:hover .icon-border {
  stroke-dashoffset: 100;
  animation: dash 2s ease 0.1s forwards;
}

@keyframes dash {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* 玻璃反光效果 - 伪元素 */
.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass-card:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

/* 添加卡片边缘发光效果 */
.glass-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  pointer-events: none;
  background: transparent;
  box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  transition: box-shadow 0.5s ease;
}

.glass-card:hover::after {
  box-shadow: 0 0 15px 2px rgba(99, 102, 241, 0.3);
}
</style> 