[
  {
    "id": "vpa-interaction-panel",
    "name": "VPA交互面板",
    "description": "VPA主交互面板，用于显示对话和结果。当此面板激活时，通常不需要VPA头像小部件。",
    "tags": ["vpa", "assistant", "voice", "dialog"],
    "scenarios": ["all"]
  },
  {
    "id": "vpa-avatar-widget",
    "name": "VPA头像小部件",
    "description": "VPA的悬浮头像，用于实时语音反馈。在没有主交互面板时使用。",
    "tags": ["vpa", "assistant", "voice"],
    "scenarios": ["all"]
  },
  {
    "id": "dynamic-island",
    "name": "灵动岛",
    "description": "位于顶部的状态栏，用于显示关键通知和实时活动。",
    "tags": ["driving", "notification", "status"],
    "scenarios": ["all"]
  },
  {
    "id": "weather-card",
    "name": "天气卡片",
    "description": "显示当前或未来的天气信息。",
    "tags": ["weather", "info"],
    "scenarios": ["natural_commute", "kawaii_family_trip", "long_drive"]
  },
  {
    "id": "music-control-card",
    "name": "音乐控制卡片",
    "description": "提供音乐播放、暂停、切歌等媒体控制功能。",
    "tags": ["music", "media", "control"],
    "scenarios": ["all"]
  },
  {
    "id": "todo-card",
    "name": "待办事项卡片",
    "description": "显示用户的待办事项列表或日历事件。",
    "tags": ["task", "calendar", "info"],
    "scenarios": ["morning_commute", "work_commute"]
  },
  {
    "id": "quick-action-card",
    "name": "快捷操作卡片",
    "description": "提供一组常用操作的按钮，如"回家"、"呼叫常用联系人"。",
    "tags": ["action", "button", "control"],
    "scenarios": ["all"]
  },
  {
    "id": "ai-task-engine-panel",
    "name": "AI任务引擎面板",
    "description": "显示后台AI任务的规划、执行和思考过程。",
    "tags": ["ai", "task", "loading", "status"],
    "scenarios": ["complex_tasks"]
  },
  {
    "id": "navigation-detail-card",
    "name": "导航详情卡片",
    "description": "显示详细的地图和转向导航信息。",
    "tags": ["navigation", "map", "driving"],
    "scenarios": ["all_driving"]
  },
  {
    "id": "news-feed-card",
    "name": "新闻源卡片",
    "description": "展示一个滚动的新闻或信息流。",
    "tags": ["news", "info", "feed"],
    "scenarios": ["waiting", "parking"]
  },
  {
    "id": "kid-education-card",
    "name": "儿童教育卡片",
    "description": "为儿童乘客提供教育内容，包括视频、游戏和学习材料。",
    "tags": ["education", "children", "entertainment", "video"],
    "scenarios": ["family_commute", "family_trip"]
  },
  {
    "id": "pedia-card",
    "name": "百科问答卡片",
    "description": "提供百科知识问答功能，特别适合回答儿童的好奇问题。",
    "tags": ["knowledge", "qa", "children", "education"],
    "scenarios": ["family_commute", "family_trip"]
  },
  {
    "id": "order-status-card",
    "name": "订单状态卡片",
    "description": "显示外卖、购物等订单的实时状态和配送信息。",
    "tags": ["order", "delivery", "status", "shopping"],
    "scenarios": ["morning_commute", "work_commute"]
  },
  {
    "id": "smart-home-card",
    "name": "智能家居卡片",
    "description": "控制家中的智能设备，如空调、灯光、安防系统等。",
    "tags": ["smart_home", "iot", "control", "automation"],
    "scenarios": ["evening_commute", "arriving_home"]
  },
  {
    "id": "video-player-card",
    "name": "视频播放器卡片",
    "description": "大屏幕视频播放器，适合在等待或停车时观看内容。",
    "tags": ["video", "entertainment", "media", "large"],
    "scenarios": ["waiting", "parking", "charging"]
  },
  {
    "id": "news-digest-card",
    "name": "新闻摘要卡片",
    "description": "显示精选的新闻摘要和重要资讯。",
    "tags": ["news", "digest", "info", "brief"],
    "scenarios": ["waiting", "parking", "morning_commute"]
  },
  {
    "id": "ambient-sound-card",
    "name": "环境音卡片",
    "description": "提供各种环境音效，如雨声、森林声、冥想音乐等。",
    "tags": ["ambient", "sound", "relaxation", "meditation"],
    "scenarios": ["waiting", "parking", "stress_relief"]
  },
  {
    "id": "rear-seat-control-card",
    "name": "后座娱乐控制卡片",
    "description": "控制后排座椅的娱乐系统，包括动画片、游戏等。",
    "tags": ["rear_seat", "entertainment", "children", "control"],
    "scenarios": ["family_trip", "family_commute"]
  },
  {
    "id": "facility-finder-card",
    "name": "查找设施卡片",
    "description": "帮助查找附近的设施，如洗手间、餐厅、加油站等。",
    "tags": ["facility", "search", "location", "poi"],
    "scenarios": ["family_trip", "long_drive", "travel"]
  },
  {
    "id": "trip-reminder-card",
    "name": "行程提醒卡片",
    "description": "提供旅行相关的提醒，如休息时间、零食补充、景点介绍等。",
    "tags": ["trip", "reminder", "travel", "schedule"],
    "scenarios": ["family_trip", "long_drive", "travel"]
  },
  {
    "id": "service-area-card",
    "name": "服务区信息卡片",
    "description": "显示前方服务区的信息，包括距离、设施、服务等。",
    "tags": ["service_area", "highway", "facilities", "rest"],
    "scenarios": ["long_drive", "highway_driving"]
  },
  {
    "id": "driver-status-card",
    "name": "驾驶员状态卡片",
    "description": "监控和显示驾驶员的疲劳状态、注意力水平等安全信息。",
    "tags": ["driver", "fatigue", "safety", "monitoring"],
    "scenarios": ["long_drive", "highway_driving", "night_driving"]
  },
  {
    "id": "vehicle-status-card",
    "name": "车辆状态卡片",
    "description": "显示车辆的关键状态信息，如续航里程、电量、油量等。",
    "tags": ["vehicle", "status", "battery", "fuel", "range"],
    "scenarios": ["long_drive", "highway_driving", "charging"]
  },
  {
    "id": "charging-status-card",
    "name": "充电状态卡片",
    "description": "显示电动车充电的实时状态、进度和预计完成时间。",
    "tags": ["charging", "electric", "battery", "progress"],
    "scenarios": ["charging", "ev_charging"]
  },
  {
    "id": "entertainment-recommendation-card",
    "name": "娱乐推荐卡片",
    "description": "在充电或等待期间推荐电影、音乐、播客等娱乐内容。",
    "tags": ["entertainment", "recommendation", "media", "waiting"],
    "scenarios": ["charging", "waiting", "parking"]
  },
  {
    "id": "nearby-services-card",
    "name": "附近服务卡片",
    "description": "显示充电站或停车点附近的商店、餐厅、咖啡店等服务。",
    "tags": ["nearby", "services", "poi", "convenience"],
    "scenarios": ["charging", "parking", "waiting"]
  },
  {
    "id": "emergency-contact-card",
    "name": "紧急联系卡片",
    "description": "在紧急情况下快速联系救援、家人或保险公司。",
    "tags": ["emergency", "contact", "rescue", "safety"],
    "scenarios": ["emergency", "accident", "breakdown"]
  },
  {
    "id": "first-aid-guide-card",
    "name": "急救指导卡片",
    "description": "提供基础的急救指导和安全建议。",
    "tags": ["first_aid", "safety", "emergency", "guide"],
    "scenarios": ["emergency", "accident"]
  },
  {
    "id": "location-sharing-card",
    "name": "位置共享卡片",
    "description": "在紧急情况下自动分享精确位置给救援部门和家人。",
    "tags": ["location", "sharing", "emergency", "gps"],
    "scenarios": ["emergency", "accident", "breakdown"]
  },
  {
    "id": "user-profile-card",
    "name": "用户配置卡片",
    "description": "显示和切换不同用户的个人配置文件。",
    "tags": ["user", "profile", "personalization", "settings"],
    "scenarios": ["multi_user", "family", "shared_vehicle"]
  },
  {
    "id": "privacy-mode-card",
    "name": "隐私模式卡片",
    "description": "在访客模式下保护用户隐私，隐藏个人信息。",
    "tags": ["privacy", "guest", "security", "protection"],
    "scenarios": ["guest_mode", "valet_mode"]
  },
  {
    "id": "basic-control-card",
    "name": "基础控制卡片",
    "description": "提供基本的车辆控制功能，如空调、车窗等。",
    "tags": ["basic", "control", "climate", "windows"],
    "scenarios": ["guest_mode", "basic_mode"]
  },
  {
    "id": "parking-assist-card",
    "name": "泊车辅助卡片",
    "description": "提供智能泊车辅助功能，包括车位搜索和自动泊车。",
    "tags": ["parking", "assist", "automation", "sensors"],
    "scenarios": ["parking", "urban_driving"]
  },
  {
    "id": "pet-care-card",
    "name": "宠物照护卡片",
    "description": "在宠物模式下监控车内温度和宠物状态。",
    "tags": ["pet", "care", "temperature", "monitoring"],
    "scenarios": ["pet_mode", "pet_care"]
  },
  {
    "id": "car-wash-checklist-card",
    "name": "洗车检查卡片",
    "description": "在洗车模式下显示车辆准备状态检查清单。",
    "tags": ["car_wash", "checklist", "preparation", "safety"],
    "scenarios": ["car_wash_mode"]
  },
  {
    "id": "romantic-ambiance-card",
    "name": "浪漫氛围卡片",
    "description": "控制车内氛围灯光和音乐，营造浪漫氛围。",
    "tags": ["romantic", "ambiance", "lighting", "mood"],
    "scenarios": ["romantic_mode", "date_night"]
  }
]
