<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI 场景布局生成器测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        select, textarea, input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        select:focus, textarea:focus, input:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            height: 200px;
            font-family: 'Courier New', monospace;
            resize: vertical;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .loading {
            text-align: center;
            color: #667eea;
        }
        .preset-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .preset-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        .preset-btn:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        .preset-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 AI HMI 场景布局生成器</h1>

        <form id="testForm">
            <div class="form-group">
                <label for="webhookUrl">Webhook URL:</label>
                <input type="url" id="webhookUrl" value="http://************:7869/webhook/ai-hmi-improved" placeholder="http://your-n8n-instance.com/webhook/test-simple" required>
                <small style="color: #666; font-size: 12px;">测试用简单webhook路径</small>
            </div>

            <div class="form-group">
                <label for="sceneDescription">场景描述:</label>
                <div class="preset-buttons">
                    <div class="preset-btn active" data-value="早高峰通勤，需要送孩子上学然后去公司">早高峰通勤</div>
                    <div class="preset-btn" data-value="雨夜深夜下班回家，感觉疲惫需要放松">雨夜归途</div>
                    <div class="preset-btn" data-value="在充电站等待充电，想看看视频或新闻">充电等待</div>
                    <div class="preset-btn" data-value="周末和家人一起去公园游玩">家庭出游</div>
                    <div class="preset-btn" data-value="长途高速驾驶，需要保持专注和警惕">长途驾驶</div>
                </div>
                <textarea id="sceneDescription" rows="4" placeholder="请描述您的驾驶场景，例如：早高峰通勤，需要送孩子上学然后去公司..." required>早高峰通勤，需要送孩子上学然后去公司</textarea>
            </div>

            <button type="submit" id="submitBtn">🚀 生成场景布局</button>
        </form>

        <div class="results" id="results" style="display: none;">
            <h2>📊 生成结果</h2>
            <div class="result-section">
                <h3>🔍 场景分析</h3>
                <pre id="sceneAnalysis"></pre>
            </div>
            <div class="result-section">
                <h3>🎨 布局设计</h3>
                <pre id="layoutDesign"></pre>
            </div>
            <div class="result-section">
                <h3>🎯 生成的布局 JSON</h3>
                <pre id="layoutJson"></pre>
            </div>
            <div class="result-section">
                <h3>🎨 渲染的 HTML</h3>
                <div class="html-preview">
                    <div class="preview-header">
                        <span>预览</span>
                        <button id="copyHtmlBtn" class="copy-btn">📋 复制 HTML</button>
                    </div>
                    <iframe id="htmlPreview"></iframe>
                </div>
                <details>
                    <summary>查看 HTML 源码</summary>
                    <pre id="htmlCode"></pre>
                </details>
            </div>
        </div>
    </div>

    <script>
        // 预设按钮功能
        document.querySelectorAll('.preset-buttons').forEach(container => {
            const buttons = container.querySelectorAll('.preset-btn');
            const textarea = container.nextElementSibling;

            buttons.forEach(btn => {
                btn.addEventListener('click', () => {
                    buttons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    textarea.value = btn.dataset.value;
                });
            });
        });

        // 表单提交
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const results = document.getElementById('results');

            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ 生成中...';
            results.style.display = 'block';
            results.innerHTML = '<div class="loading"><p>正在分析场景并生成 HMI 布局，请稍候...</p></div>';

            try {
                const webhookUrl = document.getElementById('webhookUrl').value;
                const sceneDescription = document.getElementById('sceneDescription').value;

                const payload = {
                    scene_description: sceneDescription
                };
                
                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();

                if (data.success) {
                    // 重新构建结果区域
                    results.innerHTML = `
                        <h2>📊 生成结果</h2>
                        <div class="result-section">
                            <h3>🔍 场景分析</h3>
                            <pre id="sceneAnalysis">${data.sceneAnalysis || '暂无场景分析'}</pre>
                        </div>
                        <div class="result-section">
                            <h3>🎨 布局设计</h3>
                            <pre id="layoutDesign">${data.layoutDesign || '暂无布局设计说明'}</pre>
                        </div>
                        <div class="result-section">
                            <h3>🎯 生成的布局 JSON</h3>
                            <pre id="layoutJson">${JSON.stringify(data.layoutJSON, null, 2) || '暂无布局JSON'}</pre>
                        </div>
                        <div class="result-section">
                            <h3>🎨 渲染的 HTML</h3>
                            <div class="html-preview">
                                <div class="preview-header">
                                    <span>预览</span>
                                    <button id="copyHtmlBtn" class="copy-btn">📋 复制 HTML</button>
                                </div>
                                <iframe id="htmlPreview" style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 8px;"></iframe>
                            </div>
                            <details>
                                <summary>查看 HTML 源码</summary>
                                <pre id="htmlCode">${data.htmlCode || '暂无HTML代码'}</pre>
                            </details>
                        </div>
                    `;

                    // 重新设置iframe内容和复制按钮
                    setTimeout(() => {
                        const iframe = document.getElementById('htmlPreview');
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        iframeDoc.open();
                        iframeDoc.write(data.htmlCode || '<p>暂无预览内容</p>');
                        iframeDoc.close();

                        document.getElementById('copyHtmlBtn').onclick = () => {
                            navigator.clipboard.writeText(data.htmlCode).then(() => {
                                alert('HTML代码已复制到剪贴板！');
                            });
                        };
                    }, 100);
                } else {
                    throw new Error(data.error || '未知错误');
                }
                
            } catch (error) {
                results.innerHTML = `
                    <div class="error">
                        <h3>❌ 生成失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <p><strong>请检查:</strong></p>
                        <ul>
                            <li>Webhook URL 是否正确</li>
                            <li>n8n 工作流是否已激活</li>
                            <li>API 密钥是否配置正确</li>
                            <li>网络连接是否正常</li>
                        </ul>
                    </div>
                `;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 生成场景布局';
            }
        });
    </script>
</body>
</html>
