# AI座舱API接口与数据结构 (V8.0)

本文档定义了前端(UI)与后端(AI)之间通信所使用的JSON数据结构。在HTML原型中，这将由`ai_simulator.js`生成，在实际产品中，这将是真实的API契约。

---

## 1. 前端 -> AI: 情景上下文 (Context)

前端需要向AI持续提供或在事件触发时传递当前的情景上下文。

**数据结构:**
```json
{
  "timestamp": "2025-07-26T08:00:00Z",
  "user_state": "driving", // driving, parked, in_call
  "user_preferences": {
    "disliked_music_genres": ["jazz"],
    "preferred_coffee_shop": "Starbucks"
  },
  "location": {
    "lat": 39.9042,
    "lon": 116.4074
  },
  "environment": {
    "weather": "sunny",
    "time_of_day": "morning"
  },
  "vehicle": {
    "speed": 60, // km/h
    "battery_level": 85 // in percent
  },
  "calendar_events": [
    {
      "title": "客户交流",
      "start_time": "2025-07-26T09:30:00Z"
    }
  ],
  "navigation": {
    "destination": "公司",
    "eta_seconds": 1800
  },
  "last_user_action": {
    "type": "click", // or "voice_command"
    "component": "QuickActionCard",
    "action": "navigate_home"
  }
}
```

---

## 2. AI -> 前端: 桌面渲染计划 (Render Plan)

AI接收到上下文后，返回一个完整的“渲染计划”，指导前端如何绘制整个界面。

**数据结构:**
```json
{
  "scene_id": "scene-1689-commute", // 唯一ID，用于跟踪场景
  "theme_name": "Serenity", // 新增: 从主题库中选择的主题名称
  "transition_effect": "Angled_Panel_Wipe", // 指定要使用的过渡特效ID
  "background": {
    "type": "image", // "image", "video", "map"
    "source": "/images/generated/sunset_highway.png",
    "generation_prompt": "A peaceful, wide-angle view of a highway at sunset, cinematic, warm colors"
  },
  "ambiance_effect": {
    "type": "Floating_Dust" // "Rainy", "Snowy", null
  },
  "dynamic_theme": {
    "background_color": "#3a2a4d", // 提取出的卡片背景色
    "accent_color": "#ff8c00",     // 提取出的高亮/图标色
    "text_color": "#ffffff"
  },
  "dynamic_island": {
    "icon": "icon-nav-arrow",
    "text": "8:00 前往: XX幼儿园, 预计30分到达"
  },
  "layout_plan": {
    "grid_8x4": [
      {
        "component_id": "vpa-avatar-1",
        "component_name": "VPA_Avatar",
        "grid_position": {"x": 0, "y": 2, "w": 2, "h": 2},
        "props": {
          "state": "calm"
        }
      },
      {
        "component_id": "music-card-1",
        "component_name": "MusicControlCard",
        "grid_position": {"x": 4, "y": 2, "w": 4, "h": 2},
        "props": {
          "track_name": "唐诗三百首",
          "artist": "儿童教育",
          "is_playing": true
        }
      },
      {
        "component_id": "todo-card-1",
        "component_name": "TodoCard",
        "grid_position": {"x": 4, "y": 0, "w": 4, "h": 2},
        "props": {
          "items": [
            "09:30 客户交流",
            "11:00 项目汇报"
          ]
        }
      }
    ]
  },
  "dialog_panel": null // or object to show a dialog
}
```

### 2.1. 临时对话面板的渲染计划

当需要显示临时对话面板时，`dialog_panel`字段会被填充。

**数据结构:**
```json
{
  // ...其他渲染计划字段保持不变
  "dialog_panel": {
    "panel_id": "dialog-coffee-1",
    "icon": "icon-coffee",
    "text": "现在时间充足, 附近有家常去的咖啡厅, 要不要去坐坐?",
    "options": [
      {
        "label": "取消",
        "action": "dialog_response",
        "value": "cancel_coffee"
      },
      {
        "label": "好的",
        "action": "dialog_response",
        "value": "accept_coffee"
      }
    ]
  }
}
```
当用户点击按钮后，前端会发出一个包含`action`和`value`的事件给AI，用于AI的下一步决策。

---

## 3. AI内容生成接口规范

基于现有的ComfyUI集成架构，本系统提供了多种AI内容生成能力。以下接口复用现有能力，**不能重复造轮子**。

### 3.1. 文生图接口 (壁纸生成)

**接口路径**: `POST /kolors/text-to-image`
**参考实现**: `d:\code\pythonWork\theme\theme_backend\app\api\kolors.py`
**功能**: 根据文本描述生成壁纸图片

#### 请求参数
```json
{
  "prompt": "一个美丽的日落海滩场景，温暖的色调，电影级画质",
  "task_id": "wallpaper_20240318_001"
}
```

#### 响应数据
```json
{
  "image_url": "http://example.com/generated/wallpaper_20240318_001.png"
}
```

#### 客户端调用示例
**参考路径**: `d:\code\pythonWork\theme\theme_ui\src\api\themeApi.ts` (第165-176行)
```typescript
// 文本生成壁纸
textToImage: async (prompt: string, taskId: string) => {
    try {
        const response = await axios.post(API_CONFIG.getApiUrl(`/kolors/text-to-image`), {
            prompt,
            task_id: taskId
        });
        return response.data;
    } catch (error) {
        console.error('启动壁纸生成任务失败:', error);
        throw error;
    }
}
```

### 3.2. 涂色书生成接口

**接口路径**: `POST /coloring-book/start`
**参考实现**: `d:\code\pythonWork\theme\theme_backend\app\api\coloring_book.py`
**功能**: 生成儿童涂色书线稿图

#### 请求参数
```json
{
  "prompt": "一只可爱的小猫咪在花园里玩耍",
  "count": 3,
  "ratio": "3:4"
}
```

#### 响应数据
```json
{
  "task_id": "coloring_20240318_001",
  "prompt_id": "comfy_prompt_12345",
  "image_urls": [
    "http://example.com/coloring_book_001_1.png",
    "http://example.com/coloring_book_001_2.png",
    "http://example.com/coloring_book_001_3.png"
  ]
}
```

#### 客户端调用示例
**参考路径**: `d:\code\pythonWork\theme\ColoringBook\js\integrated-task-system.js` (第83-127行)
```javascript
// 启动涂色书生成任务
async startGeneration(prompt, ratio, count) {
    const tasks = Array.from({ length: count }, (_, index) => ({
        prompt: prompt.trim(),
        index: index
    }));

    const options = {
        ratio: ratio,
        count: count
    };

    const results = await this.taskManager.startTasksWithQueueManagement(tasks, options);
    return results;
}
```

### 3.3. 文生视频接口

**接口路径**: `POST /wan/text-to-video`
**参考实现**: `d:\code\pythonWork\theme\theme_backend\app\api\wan.py`
**功能**: 根据文本描述生成视频

#### 请求参数
```json
{
  "prompt": "一个美丽的花园，微风轻拂，花朵摇摆",
  "task_id": "video_20240318_001"
}
```

#### 响应数据
```json
{
  "task_id": "video_20240318_001",
  "prompt_id": "comfy_video_12345",
  "video_url": "http://example.com/generated/video_001.mp4"
}
```

#### 客户端调用示例
**参考路径**: `d:\code\pythonWork\theme\theme_ui\src\api\themeApi.ts` (第147-158行)
```typescript
// 文本生成视频
textToVideo: async (prompt: string, taskId: string) => {
    try {
        const response = await axios.post(API_CONFIG.getApiUrl(`/wan/text-to-video`), {
            prompt,
            task_id: taskId
        });
        return response.data;
    } catch (error) {
        console.error('启动视频生成任务失败:', error);
        throw error;
    }
}
```

### 3.4. 图生视频接口

**接口路径**: `POST /image-to-video`
**参考实现**: `d:\code\pythonWork\theme\theme_backend\app\api\image_to_video.py`
**功能**: 根据上传的图片生成视频

#### 请求参数 (FormData)
```javascript
const formData = new FormData();
formData.append('image', imageFile);
formData.append('task_id', 'img2video_20240318_001');
formData.append('prompt', '让这张图片动起来，添加自然的动画效果');
```

#### 响应数据
```json
{
  "task_id": "img2video_20240318_001",
  "prompt_id": "comfy_img2video_12345",
  "video_url": "http://example.com/generated/img2video_001.mp4"
}
```

#### 客户端调用示例
**参考路径**: `d:\code\pythonWork\theme\theme_ui\src\api\themeApi.ts` (第258-271行)
```typescript
imageToVideo: async (formData: FormData): Promise<any> => {
    try {
        const response = await axios.post(API_CONFIG.getApiUrl(`/image-to-video`), formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
        return response.data;
    } catch (error) {
        console.error('启动图生视频任务失败:', error);
        throw error;
    }
}
```

---

## 4. ComfyUI集成架构说明

### 4.1. 架构概述

本系统基于ComfyUI工作流引擎实现AI内容生成，采用统一的调用模式，**避免重复造轮子**。

**核心组件**:
- **ComfyUI服务集群**: 提供AI模型推理能力
- **FastAPI后端**: 提供RESTful API接口
- **通用调用模块**: `d:\code\pythonWork\theme\theme_backend\app\api\common.py`
- **工作流配置**: `d:\code\pythonWork\theme\theme_backend\workApi\*.json`

### 4.2. 通用调用模式

**参考实现**: `d:\code\pythonWork\theme\theme_backend\app\api\common.py`

#### 工作流加载
```python
def load_workflow(workflow_name: str) -> Dict[str, Any]:
    """加载工作流文件"""
    workflow_path = Path(__file__).parent.parent.parent / "workApi" / workflow_name
    with open(workflow_path, "r", encoding="utf-8") as f:
        workflow = json.load(f)
    return workflow
```

#### 发送请求到ComfyUI
```python
async def send_prompt(server_name: str, workflow: Dict[str, Any]) -> Dict[str, Any]:
    """发送工作流到ComfyUI服务器"""
    prompt_data = {
        "prompt": workflow,
        "client_id": str(uuid.uuid4())
    }

    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.post(f"{get_api_url(server_name)}/prompt", json=prompt_data)
        return response.json()
```

#### 等待结果生成
```python
async def wait_for_image(server_name: str, prompt_id: str, node_id: str, subfolder: str) -> Optional[str]:
    """等待图像生成完成"""
    while retries < max_retries:
        status_data = await check_image_status(server_name, prompt_id)
        if status_data["status"] == "completed":
            # 构建图片URL并返回
            return image_url
        await asyncio.sleep(2)
```

### 4.3. 工作流配置示例

**线稿生成工作流**: `d:\code\pythonWork\theme\theme_backend\workApi\lineart.json`
```json
{
  "5": {
    "inputs": {
      "width": 768,
      "height": 1024,
      "batch_size": 1
    },
    "class_type": "EmptyLatentImage"
  },
  "35": {
    "inputs": {
      "prompt": "一只可爱的小猫咪"
    },
    "class_type": "CLIPTextEncode"
  }
}
```

**壁纸生成工作流**: `d:\code\pythonWork\theme\theme_backend\workApi\wallpaper_1920.json`
```json
{
  "35": {
    "inputs": {
      "prompt": "美丽的日落海滩"
    },
    "class_type": "CLIPTextEncode"
  },
  "9": {
    "inputs": {
      "filename_prefix": "changan/mainLine_111/releases/wallpaper/wallpaper"
    },
    "class_type": "SaveImage"
  }
}
```

---

## 5. 开发指南与注意事项

### 5.1. 复用现有能力原则

**重要**: 本项目已有完整的AI内容生成能力，**不能重复造轮子**。

#### 必须复用的组件
1. **ComfyUI调用模块**: `d:\code\pythonWork\theme\theme_backend\app\api\common.py`
2. **工作流配置**: `d:\code\pythonWork\theme\theme_backend\workApi\*.json`
3. **现有接口实现**: `d:\code\pythonWork\theme\theme_backend\app\api\*.py`

#### 客户端参考实现
1. **Vue.js前端**: `d:\code\pythonWork\theme\theme_ui\src\api\themeApi.ts`
2. **JavaScript客户端**: `d:\code\pythonWork\theme\ColoringBook\js\*.js`

### 5.2. 新接口开发流程

如需开发新的AI生成接口，请遵循以下流程：

#### 步骤1: 复用通用模块
```python
from .common import (
    load_workflow,
    send_prompt,
    wait_for_image,
    logger
)
```

#### 步骤2: 构建工作流
```python
def build_custom_workflow(prompt: str, task_id: str) -> tuple[dict, str]:
    """构建自定义工作流"""
    workflow = load_workflow("custom_workflow.json")
    workflow["35"]["inputs"]["prompt"] = prompt
    workflow["9"]["inputs"]["filename_prefix"] = f"custom_{task_id}"
    return workflow, "9"  # 返回输出节点ID
```

#### 步骤3: 实现接口
```python
@router.post("/custom-generation")
async def custom_generation(request: CustomRequest):
    """自定义生成接口"""
    workflow, output_node_id = build_custom_workflow(request.prompt, request.task_id)
    data = await send_prompt("custom_server", workflow)
    image_url = await wait_for_image("custom_server", data["prompt_id"], output_node_id, "")
    return {"image_url": image_url}
```

### 5.3. 错误处理最佳实践

#### 统一错误响应格式
```json
{
  "error": "GENERATION_FAILED",
  "message": "图像生成失败，请稍后重试",
  "details": "ComfyUI服务暂时不可用"
}
```

#### 客户端错误处理
```typescript
try {
    const result = await themeApi.textToImage(prompt, taskId);
    return result;
} catch (error) {
    console.error('生成失败:', error);
    // 显示用户友好的错误信息
    showErrorMessage('生成失败，请稍后重试');
    throw error;
}
```

### 5.4. 性能优化建议

1. **异步处理**: 所有AI生成接口都应使用异步模式
2. **超时设置**: 合理设置请求超时时间
3. **重试机制**: 实现自动重试逻辑
4. **资源清理**: 及时清理临时文件和资源

### 5.5. 部署配置

#### 服务器配置
**参考**: `d:\code\pythonWork\theme\theme_backend\系统架构.md`

- ComfyUI集群部署
- HAProxy负载均衡
- Docker容器化
- Redis状态管理

#### 环境变量配置
```bash
# ComfyUI服务地址
COMFYUI_API_URL=http://comfyui-server:8188
# 文件存储路径
STORAGE_PATH=/app/storage
# 数据库连接
DATABASE_URL=mysql://user:pass@db:3306/theme
```

---

## 6. 总结

本文档详细说明了AI座舱系统的接口规范，重点强调了**复用现有能力，不重复造轮子**的原则。

### 关键要点
1. **复用现有接口**: 直接使用已实现的AI生成接口
2. **参考客户端实现**: 基于现有前端代码进行开发
3. **遵循架构设计**: 基于ComfyUI的统一调用模式
4. **注重错误处理**: 提供用户友好的错误信息
5. **性能优化**: 异步处理和资源管理

### 参考路径汇总
- **后端接口**: `d:\code\pythonWork\theme\theme_backend\app\api\`
- **前端调用**: `d:\code\pythonWork\theme\theme_ui\src\api\themeApi.ts`
- **客户端示例**: `d:\code\pythonWork\theme\ColoringBook\js\`
- **工作流配置**: `d:\code\pythonWork\theme\theme_backend\workApi\`
- **架构文档**: `d:\code\pythonWork\theme\theme_backend\系统架构.md`

通过复用这些现有能力，可以快速构建功能完整的AI座舱应用，避免重复开发，提高开发效率。