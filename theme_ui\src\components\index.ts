/**
 * 粒子特效组件索引文件
 * 导出所有粒子特效相关组件，便于其他模块导入
 */

// 声明.vue文件模块
declare module "*.vue" {
    import { DefineComponent } from "vue";
    const component: DefineComponent<{}, {}, any>;
    export default component;
}

// 导入粒子特效组件
import ParticleEffect from './ParticleEffect.vue';
import StarBackground from './StarBackground.vue';
import ParticleCanvas from './ParticleCanvas.vue';
import GlowingLine from './GlowingLine.vue';
import StarCanvasBackground from './StarCanvasBackground.vue';
import GlassCard from './GlassCard.vue';
import GlassPanel from './GlassPanel.vue';
import SparkleEffect from './SparkleEffect.vue';
import PageTransition from './PageTransition.vue';
import ThreeCarModel from './ThreeCarModel.vue';
import CarArmorPanel from './CarArmorPanel.vue';
import CarArmorInfo from './CarArmorInfo.vue';

// 导入工具类
import ParticleEngine from '../utils/ParticleEngine';

// 导入粒子样式
import '../styles/particle-effects.css';

// 导出所有组件
export {
    // 基础组件
    GlassCard,
    GlassPanel,
    GlowingLine,
    SparkleEffect,
    StarBackground,

    // 性能优化组件
    StarCanvasBackground,
    ParticleEffect,
    ParticleCanvas,

    // 工具类
    ParticleEngine,

    // 新增组件
    PageTransition,

    // 3D车辆展示组件
    ThreeCarModel,
    CarArmorPanel,
    CarArmorInfo
}; 