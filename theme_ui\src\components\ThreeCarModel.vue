<template>
  <div class="three-car-model">
    <div ref="threeContainer" class="model-container"></div>
    <div v-if="loading" class="loading-overlay glass-card">
      <div class="spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watch,
  defineProps,
  defineEmits,
} from "vue";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader.js";

const props = defineProps({
  // 模型路径
  modelPath: {
    type: String,
    default: "/3D/fbx/qiyuan.fbx",
  },
  // 背景色
  backgroundColor: {
    type: String,
    default: "transparent",
  },
  // 车辆颜色
  carColor: {
    type: String,
    default: "#8b5cf6", // 默认紫色
  },
  // 车辆材质类型 - 'matte', 'glossy', 'metallic'
  materialType: {
    type: String,
    default: "glossy",
  },
  // 自动旋转速度
  rotationSpeed: {
    type: Number,
    default: 0,
  },
  // 粒子特效
  enableParticles: {
    type: Boolean,
    default: true,
  },
  // 粒子数量
  particleCount: {
    type: Number,
    default: 1500,
  },
  // 初始缩放比例
  initialScale: {
    type: Number,
    default: 0.03,
  },
  // 车身部件选择器，用于确定哪些部分应用颜色
  carPartsSelector: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["model-loaded", "loading-progress", "error"]);

// 场景相关变量
const threeContainer = ref<HTMLElement | null>(null);
const loading = ref(true);
const loadingText = ref("加载中...");
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let car: THREE.Group | THREE.Object3D;
let particleSystem: THREE.Points;
let animationFrameId: number;

// 初始化Three.js场景
const initThreeJs = () => {
  if (!threeContainer.value) return;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  const container = threeContainer.value;
  const aspect = container.clientWidth / container.clientHeight;
  camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
  camera.position.set(5, 3, 5);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true,
  });
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.shadowMap.enabled = true;
  container.innerHTML = "";
  container.appendChild(renderer.domElement);

  // 添加轨道控制
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.target.set(0, 0.5, 0);

  // 添加环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
  scene.add(ambientLight);

  // 添加方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
  directionalLight.position.set(5, 5, 5);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  scene.add(directionalLight);

  // 添加底部光源
  const bottomLight = new THREE.DirectionalLight(0xffffff, 0.5);
  bottomLight.position.set(0, -5, 0);
  scene.add(bottomLight);

  // 加载模型
  loadCarModel();

  // 如果启用粒子效果则创建粒子系统
  if (props.enableParticles) {
    createParticleSystem();
  }

  // 窗口调整大小事件
  window.addEventListener("resize", onWindowResize);

  // 开始动画循环
  animate();
};

// 加载FBX车辆模型
const loadCarModel = () => {
  const fbxLoader = new FBXLoader();
  loading.value = true;

  fbxLoader.load(
    props.modelPath,
    (object) => {
      // 加载成功
      car = object;

      // 调整模型尺寸和位置
      car.scale.set(props.initialScale, props.initialScale, props.initialScale);
      car.position.set(0, 0, 0);

      // 应用材质和阴影
      updateCarMaterial();

      // 添加至场景
      scene.add(car);

      // 调整相机位置以适应模型
      fitCameraToObject(camera, car, 5);

      // 更新状态
      loading.value = false;
      emit("model-loaded", { success: true });
    },
    (xhr) => {
      // 加载进度
      if (xhr.lengthComputable) {
        const percent = Math.floor((xhr.loaded / xhr.total) * 100);
        loadingText.value = `正在加载车辆模型... ${percent}%`;
        emit("loading-progress", percent);
      }
    },
    (error) => {
      // 加载错误
      console.error("加载FBX模型失败:", error);
      loading.value = false;
      emit("error", { message: "模型加载失败", error });

      // 加载简易模型作为备用
      loadSimpleCar();
    }
  );
};

// 创建简易车辆模型作为备用
const loadSimpleCar = () => {
  loadingText.value = "创建备用模型...";

  // 创建车身
  const bodyGeometry = new THREE.BoxGeometry(2, 0.75, 4);
  const bodyMaterial = new THREE.MeshPhongMaterial({
    color: props.carColor,
    shininess: 100,
  });
  car = new THREE.Group();
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
  body.castShadow = true;
  body.receiveShadow = true;
  car.add(body);

  // 创建车顶
  const roofGeometry = new THREE.BoxGeometry(1.5, 0.5, 2);
  const roofMaterial = new THREE.MeshPhongMaterial({
    color: props.carColor,
    shininess: 100,
  });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.set(0, 0.625, -0.5);
  roof.castShadow = true;
  roof.receiveShadow = true;
  car.add(roof);

  // 添加车轮
  const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 32);
  wheelGeometry.rotateZ(Math.PI / 2);
  const wheelMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });

  const wheelPositions = [
    [1.1, -0.5, 1.3], // 前右
    [-1.1, -0.5, 1.3], // 前左
    [1.1, -0.5, -1.3], // 后右
    [-1.1, -0.5, -1.3], // 后左
  ];

  wheelPositions.forEach((position) => {
    const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
    wheel.position.set(position[0], position[1], position[2]);
    wheel.castShadow = true;
    wheel.receiveShadow = true;
    car.add(wheel);
  });

  // 使用初始缩放比例
  car.scale.set(
    props.initialScale * 1.5,
    props.initialScale * 1.5,
    props.initialScale * 1.5
  );

  // 添加至场景
  scene.add(car);
  loading.value = false;
  emit("model-loaded", { success: true, isBackupModel: true });
};

// 更新车辆材质
const updateCarMaterial = () => {
  if (!car) return;

  car.traverse((child: any) => {
    if (child.isMesh && child.material) {
      // 检查是否为车身外部部件
      const isExteriorPart = checkIfExteriorPart(child);

      if (isExteriorPart) {
        // 为车身应用颜色和材质
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            material.color.set(props.carColor);

            // 设置材质属性
            if (props.materialType === "matte") {
              material.roughness = 0.9;
              material.metalness = 0.1;
            } else if (props.materialType === "glossy") {
              material.roughness = 0.2;
              material.metalness = 0.3;
              material.shininess = 100;
            } else if (props.materialType === "metallic") {
              material.roughness = 0.3;
              material.metalness = 0.8;
              material.shininess = 80;
            }
          });
        } else if (child.material) {
          child.material.color.set(props.carColor);

          // 设置材质属性
          if (props.materialType === "matte") {
            child.material.roughness = 0.9;
            child.material.metalness = 0.1;
          } else if (props.materialType === "glossy") {
            child.material.roughness = 0.2;
            child.material.metalness = 0.3;
            child.material.shininess = 100;
          } else if (props.materialType === "metallic") {
            child.material.roughness = 0.3;
            child.material.metalness = 0.8;
            child.material.shininess = 80;
          }
        }
      }

      // 应用阴影
      child.castShadow = true;
      child.receiveShadow = true;
    }
  });
};

// 检查是否是车身外部部件
const checkIfExteriorPart = (mesh: any) => {
  // 如果没有提供选择器，则默认所有部件都是外部部件
  if (!props.carPartsSelector || props.carPartsSelector.length === 0) {
    return true;
  }

  // 根据名称判断是否是外部部件
  const meshName = mesh.name.toLowerCase();

  // 检查名称是否包含选择器中的任一关键词
  return props.carPartsSelector.some((selector: string) =>
    meshName.includes(selector.toLowerCase())
  );
};

// 创建粒子系统
const createParticleSystem = () => {
  if (particleSystem) {
    scene.remove(particleSystem);
  }

  const geometry = new THREE.BufferGeometry();
  const vertices = [];
  const sizes = [];
  const colors = [];

  const color1 = new THREE.Color(0x8b5cf6); // 紫色
  const color2 = new THREE.Color(0x3b82f6); // 蓝色

  for (let i = 0; i < props.particleCount; i++) {
    // 生成围绕车辆的随机粒子位置
    const x = (Math.random() - 0.5) * 30;
    const y = (Math.random() - 0.5) * 30;
    const z = (Math.random() - 0.5) * 30;

    vertices.push(x, y, z);

    // 随机粒子大小
    sizes.push(Math.random() * 0.08 + 0.02);

    // 混合两种颜色
    const mixedColor = color1.clone().lerp(color2, Math.random());
    colors.push(mixedColor.r, mixedColor.g, mixedColor.b);
  }

  geometry.setAttribute(
    "position",
    new THREE.Float32BufferAttribute(vertices, 3)
  );
  geometry.setAttribute("size", new THREE.Float32BufferAttribute(sizes, 1));
  geometry.setAttribute("color", new THREE.Float32BufferAttribute(colors, 3));

  // 粒子材质
  const particleMaterial = new THREE.PointsMaterial({
    size: 0.05,
    vertexColors: true,
    transparent: true,
    opacity: 0.7,
  });

  particleSystem = new THREE.Points(geometry, particleMaterial);
  scene.add(particleSystem);
};

// 调整相机位置以适应模型
const fitCameraToObject = (
  camera: THREE.PerspectiveCamera,
  object: THREE.Object3D,
  offset: number
) => {
  offset = offset || 1.5;

  const boundingBox = new THREE.Box3().setFromObject(object);
  const center = boundingBox.getCenter(new THREE.Vector3());
  const size = boundingBox.getSize(new THREE.Vector3());

  // 获取最大尺寸
  const maxDim = Math.max(size.x, size.y, size.z);
  const fov = camera.fov * (Math.PI / 180);

  let cameraZ = Math.abs(maxDim / Math.sin(fov / 2));
  cameraZ *= offset;

  // 设置相机位置
  camera.position.set(
    center.x + cameraZ,
    center.y + cameraZ / 2,
    center.z + cameraZ
  );

  // 设置相机的目标
  controls.target.set(center.x, center.y, center.z);
  controls.update();
};

// 窗口大小调整
const onWindowResize = () => {
  if (!threeContainer.value) return;

  const container = threeContainer.value;
  const aspect = container.clientWidth / container.clientHeight;

  camera.aspect = aspect;
  camera.updateProjectionMatrix();
  renderer.setSize(container.clientWidth, container.clientHeight);
};

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate);

  // 模型旋转
  if (car && props.rotationSpeed > 0) {
    car.rotation.y += props.rotationSpeed * 0.01;
  }

  // 粒子系统动画
  if (particleSystem) {
    particleSystem.rotation.y += 0.001;

    // 粒子漂浮效果
    const positions = particleSystem.geometry.attributes.position.array;
    for (let i = 0; i < positions.length; i += 3) {
      positions[i + 1] += Math.sin(Date.now() * 0.001 + i) * 0.001;
    }
    particleSystem.geometry.attributes.position.needsUpdate = true;
  }

  controls.update();
  renderer.render(scene, camera);
};

// 监听属性变化并更新
watch(
  () => props.carColor,
  (newColor) => {
    if (car) updateCarMaterial();
  }
);

watch(
  () => props.materialType,
  (newType) => {
    if (car) updateCarMaterial();
  }
);

watch(
  () => props.enableParticles,
  (newValue) => {
    if (newValue) {
      createParticleSystem();
    } else if (particleSystem) {
      scene.remove(particleSystem);
      particleSystem = null;
    }
  }
);

watch(
  () => props.particleCount,
  (newCount) => {
    if (props.enableParticles) {
      createParticleSystem();
    }
  }
);

// 生命周期钩子
onMounted(() => {
  initThreeJs();
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", onWindowResize);
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }

  // 释放资源
  if (renderer) {
    renderer.dispose();
  }

  if (scene) {
    scene.clear();
  }
});

// 暴露给父组件的方法
defineExpose({
  updateCarColor: (color: string) => {
    if (car) {
      car.traverse((child: any) => {
        if (child.isMesh && child.material) {
          const isExteriorPart = checkIfExteriorPart(child);
          if (isExteriorPart) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material) => {
                material.color.set(color);
              });
            } else {
              child.material.color.set(color);
            }
          }
        }
      });
    }
  },
  resetView: () => {
    if (camera && controls && car) {
      // 获取模型尺寸
      const boundingBox = new THREE.Box3().setFromObject(car);
      const center = boundingBox.getCenter(new THREE.Vector3());
      const size = boundingBox.getSize(new THREE.Vector3());

      // 计算合适的相机位置
      const maxDim = Math.max(size.x, size.y, size.z);
      const fov = camera.fov * (Math.PI / 180);
      let cameraZ = Math.abs(maxDim / Math.sin(fov / 2)) * 1.5;

      // 重置相机位置
      camera.position.set(
        center.x + cameraZ,
        center.y + cameraZ / 2,
        center.z + cameraZ
      );
      controls.target.set(center.x, center.y, center.z);
      controls.update();
    }
  },
});
</script>

<style scoped>
.three-car-model {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 0.75rem;
  overflow: hidden;
}

.model-container {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(15, 23, 42, 0.7);
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #8b5cf6;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: white;
  font-size: 16px;
  font-weight: 500;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style> 