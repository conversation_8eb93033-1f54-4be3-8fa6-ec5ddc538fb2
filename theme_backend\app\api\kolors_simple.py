from fastapi import APIRouter, HTTPException
import requests
from pydantic import BaseModel, Field
from .common import logger
import json

router = APIRouter()

kolor_api = "http://************:15010/generate"

class TextToImageRequest(BaseModel):
    prompt: str
    task_id: str = Field(..., description="任务ID，用于文件保存和标识")

# 简化版本的text_to_image接口，用于AI-HMI测试
@router.post("/text-to-image", summary="文本生成壁纸(简化版)", description="根据文本描述生成壁纸，简化版本用于测试")
async def text_to_image_simple(request: TextToImageRequest):
    try:
        logger.info(f"\n========== AI-HMI 壁纸生成测试 ==========")
        logger.info(f"提示词: {request.prompt}")
        logger.info(f"任务ID: {request.task_id}")
        
        # 从请求体中获取文本描述
        text_description = request.prompt

        if not text_description:
            raise HTTPException(status_code=400, detail="文本描述不能为空")

        # 构建请求负载
        payload = {
            "query": text_description,
            "has_baidu_censor": False
        }

        logger.info(f"调用外部kolors API: {kolor_api}")
        logger.info(f"请求参数: {json.dumps(payload, indent=2)}")

        # 调用远程HTTP接口
        try:
            response = requests.post(f"{kolor_api}", json=payload, timeout=30)
            response.raise_for_status()  # 如果响应状态码不是200，会抛出异常
            
            # 解析响应数据
            response_data = response.json()
            image_url = response_data.get('image_url')
            logger.info(f"kolors API返回: {json.dumps(response_data, indent=2)}")

            if not image_url:
                logger.warning("kolors API返回的图像URL为空，使用fallback")
                # 返回一个测试图片URL
                return {
                    "image_url": "https://picsum.photos/800/600?random=" + str(hash(text_description) % 1000),
                    "status": "fallback",
                    "message": "使用fallback图片"
                }

            logger.info(f"✅ 成功生成图像URL: {image_url}")
            return {
                "image_url": image_url,
                "status": "success",
                "task_id": request.task_id
            }
            
        except requests.exceptions.Timeout:
            logger.error("kolors API调用超时，使用fallback")
            return {
                "image_url": "https://picsum.photos/800/600?random=" + str(hash(text_description) % 1000),
                "status": "timeout_fallback",
                "message": "API超时，使用fallback图片"
            }
        except requests.exceptions.ConnectionError:
            logger.error("kolors API连接失败，使用fallback")
            return {
                "image_url": "https://picsum.photos/800/600?random=" + str(hash(text_description) % 1000),
                "status": "connection_fallback", 
                "message": "API连接失败，使用fallback图片"
            }
        except Exception as api_error:
            logger.error(f"kolors API调用失败: {str(api_error)}，使用fallback")
            return {
                "image_url": "https://picsum.photos/800/600?random=" + str(hash(text_description) % 1000),
                "status": "api_error_fallback",
                "message": f"API错误，使用fallback图片: {str(api_error)}"
            }

    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用text_to_image接口出错: {str(e)}")
        # 即使出现异常，也返回一个fallback图片
        return {
            "image_url": "https://picsum.photos/800/600?random=999",
            "status": "error_fallback",
            "message": f"服务异常，使用fallback图片: {str(e)}"
        }

# 健康检查端点
@router.get("/health", summary="健康检查", description="检查kolors服务状态")
async def health_check():
    try:
        # 测试外部kolors API连接
        response = requests.get(kolor_api.replace('/generate', '/health'), timeout=5)
        if response.status_code == 200:
            return {"status": "healthy", "kolors_api": "available"}
        else:
            return {"status": "degraded", "kolors_api": "unavailable", "fallback": "enabled"}
    except:
        return {"status": "degraded", "kolors_api": "unavailable", "fallback": "enabled"}
