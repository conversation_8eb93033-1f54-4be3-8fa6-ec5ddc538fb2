<template>
  <div class="glass-darker rounded-xl backdrop-blur-sm" :class="customClass" :style="[
      { padding: padding }, 
      customStyle
    ]">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  // 自定义样式类
  customClass: {
    type: String,
    default: "",
  },
  // 内边距
  padding: {
    type: String,
    default: "2rem",
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style scoped>
/* 增强毛玻璃效果 - 更深色背景版本 */
.glass-darker {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}
</style> 