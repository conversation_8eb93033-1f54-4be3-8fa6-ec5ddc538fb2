"""
提示词增强API接口
处理用户提交的提示词增强请求，创建会话并启动Agent
"""

import uuid
import sys
import os
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, Dict, Any
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '../../../'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 尝试不同的导入方式
try:
    # 尝试直接从agent包导入
    from agent.promptAgent import AgentTaskManager, WebSocketManager, PromptEnhancerAgent
except ImportError:
    try:
        # 尝试从theme_backend导入
        from theme_backend.agent.promptAgent import AgentTaskManager, WebSocketManager, PromptEnhancerAgent
    except ImportError:
        # 尝试相对导入
        sys.path.append(os.path.abspath(os.path.join(current_dir, '../../')))
        from agent.promptAgent import AgentTaskManager, WebSocketManager, PromptEnhancerAgent

# 导入WebSocket管理器实例
from ..core.webscoket import websocket_manager, sessions

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(prefix="/api/enhance", tags=["enhance"])

# 创建任务管理器
task_manager = AgentTaskManager(websocket_manager)

# 请求模型
class EnhancePromptRequest(BaseModel):
    prompt: str
    system_prompt: Optional[str] = None
    model: Optional[str] = None
    user_id: Optional[str] = "anonymous"

# 响应模型
class EnhancePromptResponse(BaseModel):
    session_id: str
    status: str
    message: str

# 会话状态模型
class EnhanceStatus(BaseModel):
    session_id: str
    status: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    message: Optional[str] = None
    is_running: bool = False
    is_complete: bool = False
    error: Optional[str] = None

# 共享状态存储
enhance_sessions: Dict[str, Any] = {}

# 创建增强代理实例
prompt_enhancer_agents: Dict[str, PromptEnhancerAgent] = {}

@router.post("/prompt", response_model=EnhancePromptResponse)
async def enhance_prompt(request: EnhancePromptRequest, background_tasks: BackgroundTasks):
    """启动提示词增强任务"""
    session_id = str(uuid.uuid4())
    logger.debug(f"收到提示词增强请求: 会话ID={session_id}, 提示词={request.prompt[:50]}...")
    print(f"[API] 提示词增强请求: 会话ID={session_id}, 提示词={request.prompt[:50]}...")
    
    try:
        # 创建WebSocket会话
        sessions[session_id] = {
            "id": session_id,
            "created_at": datetime.now().isoformat(),
            "status": "created",
            "last_activity": datetime.now().isoformat(),
            "type": "enhance_prompt"
        }
        logger.debug(f"创建会话信息: 会话ID={session_id}")
        print(f"[API] 创建会话: {session_id}")
        
        # 创建增强代理实例
        agent = PromptEnhancerAgent(websocket_manager=websocket_manager)
        prompt_enhancer_agents[session_id] = agent
        logger.debug(f"创建增强代理: 会话ID={session_id}")
        print(f"[API] 创建增强代理: {session_id}")
        
        # 创建会话状态
        enhance_sessions[session_id] = {
            "session_id": session_id,
            "status": "准备中",
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "message": "任务正在准备中",
            "is_running": True,
            "is_complete": False,
            "error": None,
            "prompt": request.prompt,
            "system_prompt": request.system_prompt,
            "model": request.model or "gpt-4-turbo-preview",
            "user_id": request.user_id,
            "agent": agent
        }
        logger.debug(f"创建增强任务状态: 会话ID={session_id}")
        print(f"[API] 创建任务状态: {session_id}")
        
        # 在后台启动增强任务
        background_tasks.add_task(
            run_enhance_task,
            session_id=session_id,
            prompt=request.prompt,
            system_prompt=request.system_prompt,
            model=request.model,
            user_id=request.user_id
        )
        logger.info(f"任务已添加到后台: 会话ID={session_id}")
        print(f"[API] 任务已添加到后台: {session_id}")
        
        return EnhancePromptResponse(
            session_id=session_id,
            status="已启动",
            message="提示词增强任务已启动，请通过WebSocket连接获取实时结果"
        )
    
    except Exception as e:
        logger.error(f"启动提示词增强任务失败: 会话ID={session_id}, 错误={str(e)}")
        print(f"[API错误] 启动增强任务失败: {session_id}, 错误={str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"[API错误堆栈] {traceback.format_exc()}")
        
        raise HTTPException(
            status_code=500,
            detail=f"启动提示词增强任务失败: {str(e)}"
        )

@router.get("/status/{session_id}", response_model=EnhanceStatus)
async def get_enhance_status(session_id: str):
    """获取提示词增强任务状态"""
    logger.debug(f"查询任务状态: 会话ID={session_id}")
    print(f"[API] 查询任务状态: {session_id}")
    
    if session_id not in enhance_sessions:
        logger.warning(f"查询不存在的会话: 会话ID={session_id}")
        print(f"[API警告] 查询不存在的会话: {session_id}")
        raise HTTPException(
            status_code=404,
            detail=f"提示词增强会话不存在: {session_id}"
        )
    
    # 获取会话状态
    session = enhance_sessions[session_id]
    logger.debug(f"获取会话状态: 会话ID={session_id}, 状态={session['status']}")
    print(f"[API] 会话状态: {session_id}, 状态={session['status']}")
    
    return EnhanceStatus(
        session_id=session_id,
        status=session["status"],
        start_time=session["start_time"],
        end_time=session["end_time"],
        message=session["message"],
        is_running=session["is_running"],
        is_complete=session["is_complete"],
        error=session["error"]
    )

@router.post("/cancel/{session_id}")
async def cancel_enhance_task(session_id: str):
    """取消提示词增强任务"""
    logger.debug(f"取消任务请求: 会话ID={session_id}")
    print(f"[API] 取消任务请求: {session_id}")
    
    if session_id not in enhance_sessions:
        logger.warning(f"尝试取消不存在的会话: 会话ID={session_id}")
        print(f"[API警告] 尝试取消不存在的会话: {session_id}")
        raise HTTPException(
            status_code=404,
            detail=f"提示词增强会话不存在: {session_id}"
        )
    
    # 获取会话信息
    session = enhance_sessions[session_id]
    agent = session.get("agent")
    
    if agent and session["is_running"] and not session["is_complete"]:
        # 尝试取消任务
        try:
            logger.info(f"正在取消任务: 会话ID={session_id}")
            print(f"[API] 正在取消任务: {session_id}")
            
            # 取消任务
            if hasattr(agent, "cancel_task") and callable(agent.cancel_task):
                await agent.cancel_task()
                
            # 更新状态
            session["status"] = "已取消"
            session["is_running"] = False
            session["is_complete"] = True
            session["end_time"] = datetime.now().isoformat()
            session["message"] = "任务已取消"
            
            logger.info(f"任务已成功取消: 会话ID={session_id}")
            print(f"[API] 任务已成功取消: {session_id}")
            
            return {"status": "success", "message": "任务已取消"}
        
        except Exception as e:
            logger.error(f"取消任务失败: 会话ID={session_id}, 错误={str(e)}")
            print(f"[API错误] 取消任务失败: {session_id}, 错误={str(e)}")
            
            raise HTTPException(
                status_code=500,
                detail=f"取消任务失败: {str(e)}"
            )
    else:
        logger.warning(f"任务不能被取消: 会话ID={session_id}, 运行中={session['is_running']}, 已完成={session['is_complete']}")
        print(f"[API警告] 任务不能被取消: {session_id}, 运行中={session['is_running']}, 已完成={session['is_complete']}")
        
        return {"status": "not_cancelled", "message": "任务不在运行状态或已完成"}

# 后台任务函数
async def run_enhance_task(session_id: str, prompt: str, system_prompt: Optional[str] = None, 
                          model: Optional[str] = None, user_id: str = "anonymous"):
    """运行提示词增强任务的后台任务"""
    logger.debug(f"开始执行后台任务: 会话ID={session_id}")
    print(f"[后台任务] 开始执行: {session_id}")
    
    try:
        # 更新会话状态
        if session_id in enhance_sessions:
            enhance_sessions[session_id]["status"] = "运行中"
            enhance_sessions[session_id]["message"] = "正在增强提示词"
            logger.debug(f"更新任务状态为运行中: 会话ID={session_id}")
            print(f"[后台任务] 状态=运行中: {session_id}")
        
        # 获取代理对象
        agent = prompt_enhancer_agents.get(session_id)
        if not agent:
            logger.error(f"找不到代理对象: 会话ID={session_id}")
            print(f"[后台任务错误] 找不到代理对象: {session_id}")
            
            # 更新状态
            if session_id in enhance_sessions:
                enhance_sessions[session_id]["status"] = "错误"
                enhance_sessions[session_id]["is_running"] = False
                enhance_sessions[session_id]["is_complete"] = True
                enhance_sessions[session_id]["end_time"] = datetime.now().isoformat()
                enhance_sessions[session_id]["message"] = "找不到代理对象"
                enhance_sessions[session_id]["error"] = "找不到代理对象"
            
            return
        
        # 执行增强任务
        logger.info(f"调用代理增强提示词: 会话ID={session_id}")
        print(f"[后台任务] 调用代理: {session_id}")
        result = await agent.enhance_prompt(session_id, prompt, user_id)
        
        # 更新任务状态
        if session_id in enhance_sessions:
            if result:
                # 任务成功
                enhance_sessions[session_id]["status"] = "已完成"
                enhance_sessions[session_id]["is_running"] = False
                enhance_sessions[session_id]["is_complete"] = True
                enhance_sessions[session_id]["end_time"] = datetime.now().isoformat()
                enhance_sessions[session_id]["message"] = "提示词增强已完成"
                enhance_sessions[session_id]["result"] = result
                logger.info(f"任务成功完成: 会话ID={session_id}")
                print(f"[后台任务] 完成: {session_id}")
            else:
                # 任务失败或被取消
                enhance_sessions[session_id]["status"] = "失败"
                enhance_sessions[session_id]["is_running"] = False
                enhance_sessions[session_id]["is_complete"] = True
                enhance_sessions[session_id]["end_time"] = datetime.now().isoformat()
                enhance_sessions[session_id]["message"] = "提示词增强失败"
                enhance_sessions[session_id]["error"] = "任务执行失败，未返回结果"
                logger.warning(f"任务执行失败: 会话ID={session_id}")
                print(f"[后台任务] 失败: {session_id}")
    
    except asyncio.CancelledError:
        # 任务被取消
        logger.info(f"任务被取消: 会话ID={session_id}")
        print(f"[后台任务] 被取消: {session_id}")
        
        # 更新状态
        if session_id in enhance_sessions:
            enhance_sessions[session_id]["status"] = "已取消"
            enhance_sessions[session_id]["is_running"] = False
            enhance_sessions[session_id]["is_complete"] = True
            enhance_sessions[session_id]["end_time"] = datetime.now().isoformat()
            enhance_sessions[session_id]["message"] = "任务已取消"
    
    except Exception as e:
        # 任务出错
        logger.error(f"任务执行出错: 会话ID={session_id}, 错误={str(e)}")
        print(f"[后台任务错误] 执行出错: {session_id}, 错误={str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"[后台任务错误堆栈] {traceback.format_exc()}")
        
        # 更新状态
        if session_id in enhance_sessions:
            enhance_sessions[session_id]["status"] = "错误"
            enhance_sessions[session_id]["is_running"] = False
            enhance_sessions[session_id]["is_complete"] = True
            enhance_sessions[session_id]["end_time"] = datetime.now().isoformat()
            enhance_sessions[session_id]["message"] = f"执行时出错: {str(e)}"
            enhance_sessions[session_id]["error"] = str(e)
    
    finally:
        # 清理资源
        logger.debug(f"后台任务执行完成: 会话ID={session_id}")
        print(f"[后台任务] 清理资源: {session_id}") 