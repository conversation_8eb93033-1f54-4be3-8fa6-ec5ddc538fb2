[{"id": "morning_family_commute", "name": "早高峰家庭通勤", "description": "工作日早晨，带孩子上学后前往公司的复合场景", "phases": [{"phase": "family_phase", "description": "送孩子上学阶段", "duration": "15-20分钟", "key_components": ["kid-education-card", "pedia-card", "navigation-detail-card"], "atmosphere": "温馨、教育、互动"}, {"phase": "work_phase", "description": "独自前往公司阶段", "duration": "25-30分钟", "key_components": ["music-control-card", "todo-card", "order-status-card"], "atmosphere": "专注、高效、放松"}], "triggers": ["工作日", "早高峰", "多乘客", "儿童检测"], "layout_preference": {"suggestion": ["adaptive-layout", "phase-switching"], "constraints": {"smooth_transition": true}}}, {"id": "evening_commute_home", "name": "下班回家通勤", "description": "下班后回家，可能途径购物或其他生活服务", "phases": [{"phase": "decompression", "description": "下班放松阶段", "duration": "全程", "key_components": ["music-control-card", "smart-home-card", "weather-card"], "atmosphere": "放松、舒缓、温馨"}], "triggers": ["下班时间", "目的地为家", "疲劳检测"], "layout_preference": {"suggestion": ["relaxing-layout", "home-preparation"], "constraints": {"minimal_distraction": true}}}, {"id": "waiting_parking", "name": "车内等待/停车", "description": "在停车状态下的娱乐和休息场景", "phases": [{"phase": "entertainment", "description": "娱乐休息阶段", "duration": "不定", "key_components": ["video-player-card", "news-digest-card", "ambient-sound-card"], "atmosphere": "休闲、娱乐、放松"}], "triggers": ["P档", "驻车超过1分钟", "车内有乘客"], "layout_preference": {"suggestion": ["entertainment-focused", "large-content"], "constraints": {"maximize_screen_usage": true}}}, {"id": "rainy_night_drive", "name": "雨夜归途", "description": "深夜雨天驾驶，需要专注和安全感", "phases": [{"phase": "focused_driving", "description": "专注驾驶阶段", "duration": "全程", "key_components": ["navigation-detail-card", "music-control-card", "vpa-avatar-widget"], "atmosphere": "专注、安全、温暖"}], "triggers": ["夜间", "雨天", "疲劳检测"], "layout_preference": {"suggestion": ["minimal-ui", "atmospheric"], "constraints": {"reduce_visual_noise": true}}}, {"id": "family_weekend_trip", "name": "周末家庭出游", "description": "周末带家人出游的愉快旅程", "phases": [{"phase": "family_entertainment", "description": "家庭娱乐阶段", "duration": "全程", "key_components": ["rear-seat-control-card", "facility-finder-card", "trip-reminder-card"], "atmosphere": "欢乐、互动、便利"}], "triggers": ["周末", "家庭出游目的地", "多乘客"], "layout_preference": {"suggestion": ["family-friendly", "multi-user"], "constraints": {"child_accessible": true}}}, {"id": "highway_long_drive", "name": "长途高速驾驶", "description": "高速公路长距离驾驶场景", "phases": [{"phase": "highway_monitoring", "description": "高速监控阶段", "duration": "全程", "key_components": ["service-area-card", "driver-status-card", "vehicle-status-card"], "atmosphere": "安全、监控、便利"}], "triggers": ["高速公路", "连续驾驶超过1小时"], "layout_preference": {"suggestion": ["safety-focused", "information-dense"], "constraints": {"safety_priority": true}}}, {"id": "guest_valet_mode", "name": "访客/代驾模式", "description": "保护隐私的访客使用模式", "phases": [{"phase": "privacy_protection", "description": "隐私保护阶段", "duration": "全程", "key_components": ["privacy-mode-card", "basic-control-card", "navigation-detail-card"], "atmosphere": "简洁、安全、功能受限"}], "triggers": ["手动激活", "访客模式"], "layout_preference": {"suggestion": ["minimal-features", "privacy-first"], "constraints": {"hide_personal_data": true}}}, {"id": "ev_charging", "name": "智能充电场景", "description": "电动车充电时的智能服务场景", "phases": [{"phase": "charging_entertainment", "description": "充电娱乐阶段", "duration": "充电时长", "key_components": ["charging-status-card", "entertainment-recommendation-card", "nearby-services-card"], "atmosphere": "便利、娱乐、高效"}], "triggers": ["电量低于30%", "到达充电站"], "layout_preference": {"suggestion": ["charging-optimized", "time-killing"], "constraints": {"charging_info_priority": true}}}, {"id": "fatigue_detection", "name": "疲劳驾驶检测", "description": "检测到驾驶员疲劳时的安全干预场景", "phases": [{"phase": "safety_intervention", "description": "安全干预阶段", "duration": "直到安全", "key_components": ["driver-status-card", "service-area-card", "emergency-contact-card"], "atmosphere": "警示、安全、关怀"}], "triggers": ["疲劳检测", "异常驾驶行为"], "layout_preference": {"suggestion": ["alert-focused", "safety-critical"], "constraints": {"immediate_attention": true}}}, {"id": "multi_user_switching", "name": "多用户识别切换", "description": "识别和切换不同用户配置的场景", "phases": [{"phase": "user_identification", "description": "用户识别阶段", "duration": "识别过程", "key_components": ["user-profile-card", "privacy-mode-card"], "atmosphere": "个性化、安全、便利"}], "triggers": ["新用户检测", "用户切换请求"], "layout_preference": {"suggestion": ["user-centric", "personalization"], "constraints": {"quick_switching": true}}}, {"id": "smart_parking", "name": "智能泊车辅助", "description": "智能寻找车位和泊车辅助场景", "phases": [{"phase": "parking_assistance", "description": "泊车辅助阶段", "duration": "泊车过程", "key_components": ["parking-assist-card", "nearby-services-card"], "atmosphere": "精确、辅助、便利"}], "triggers": ["接近目的地", "搜索停车位"], "layout_preference": {"suggestion": ["parking-focused", "sensor-data"], "constraints": {"real_time_feedback": true}}}, {"id": "emergency_situation", "name": "紧急情况处理", "description": "事故或紧急情况的应急响应场景", "phases": [{"phase": "emergency_response", "description": "紧急响应阶段", "duration": "直到救援", "key_components": ["emergency-contact-card", "first-aid-guide-card", "location-sharing-card"], "atmosphere": "紧急、救援、安全"}], "triggers": ["碰撞检测", "气囊弹出", "手动求救"], "layout_preference": {"suggestion": ["emergency-critical", "life-saving"], "constraints": {"maximum_urgency": true}}}, {"id": "pet_care_mode", "name": "宠物照护模式", "description": "短暂留宠物在车内的安全监护场景", "phases": [{"phase": "pet_monitoring", "description": "宠物监护阶段", "duration": "主人离开期间", "key_components": ["pet-care-card"], "atmosphere": "安全、监控、温馨"}], "triggers": ["宠物模式激活"], "layout_preference": {"suggestion": ["pet-focused", "monitoring"], "constraints": {"temperature_priority": true}}}, {"id": "car_wash_mode", "name": "洗车模式", "description": "准备进入洗车机的车辆设置场景", "phases": [{"phase": "wash_preparation", "description": "洗车准备阶段", "duration": "洗车过程", "key_components": ["car-wash-checklist-card"], "atmosphere": "准备、安全、自动化"}], "triggers": ["洗车模式激活"], "layout_preference": {"suggestion": ["checklist-focused", "safety-prep"], "constraints": {"clear_instructions": true}}}, {"id": "romantic_mode", "name": "浪漫二人世界", "description": "营造浪漫氛围的私密场景", "phases": [{"phase": "romantic_ambiance", "description": "浪漫氛围阶段", "duration": "浪漫时光", "key_components": ["romantic-ambiance-card", "music-control-card"], "atmosphere": "浪漫、私密、温馨"}], "triggers": ["浪漫模式激活", "特殊日期"], "layout_preference": {"suggestion": ["atmospheric", "minimal-ui"], "constraints": {"mood_lighting": true}}}]