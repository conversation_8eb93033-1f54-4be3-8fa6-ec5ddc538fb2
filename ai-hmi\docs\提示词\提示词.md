# AI HMI 布局生成提示词指南 (V2)

## 核心理念

新版的提示词系统旨在实现**精确、结构化、可预测**的布局生成。我们不再用模糊的自然语言描述界面，而是通过一个清晰的结构来“指挥”AI进行布局设计。AI的角色从“自由创作者”转变为“专业的布局设计师”，严格遵循我们提供的蓝图。

核心是**“布局JSON”**，它定义了界面的骨架。

## 提示词结构

一个标准的提示词由以下部分组成：

1.  **角色 (Role):** 固定为 `你是一位专业的HMI布局设计师`。
2.  **任务 (Task):** 描述总体目标，并指令AI使用知识库。
3.  **场景 (Scene):** 从 `Scene_Element_KB.json` 中选择一个场景ID。
4.  **风格 (Style):** 从 `Style_KB.json` 中选择一个或多个风格ID。
5.  **布局 (Layout):** 这是核心，一个描述界面结构的JSON对象。
6.  **输出格式 (Output):** 要求AI输出最终的HTML代码。

---

## 布局JSON (`Layout`)

这是描述布局的语言。它由嵌套的“容器”和“组件”构成。

*   **容器 (Container):**
    *   `container_id`: 必须是 `Layout_KB.json` 中定义的布局容器ID。
    *   `slots`: 一个对象，key是容器定义的插槽ID，value是该插槽中要放置的内容（可以是另一个容器，也可以是一个组件）。
*   **组件 (Component):**
    *   `component_id`: 必须是 `Component_KB.json` 中定义的原子组件ID。
    *   `params`: (可选) 一个对象，用于传递给该组件的特定参数（例如，图表的数据）。

---

## 完整示例

### 示例1：赛博朋克驾驶界面

**目标:** 创建一个信息密集的驾驶界面，左侧是关键的地图导航，右侧是媒体和性能信息。

```markdown
**角色:** 你是一位专业的HMI布局设计师。

**任务:** 
1.  根据下方提供的 `场景ID` 和 `风格ID`，理解整体的氛围和用户意图。
2.  严格遵循 `布局` JSON对象定义的结构，将其渲染为完整的HTML代码。
3.  **对于布局中的每一个组件，请根据场景和风格，为其生成贴切、生动的占位内容。** 例如，在`cyberpunk_drive`场景下，音乐卡片不应是默认的“歌曲名”，而应是“Synthwave Beats”或“未来都市脉冲”等符合氛围的名称。

**场景:** `cyberpunk_drive`

**风格:** `cyberpunk`, `glassmorphism`

**布局:**
```json
{
  "layout": {
    "container_id": "two-column-container",
    "slots": {
      "left-panel": {
        "component_id": "navigation-detail-card",
        "params": {
          "zoom_level": 15
        }
      },
      "main-content": {
        "layout": {
          "container_id": "vertical-stack-container",
          "slots": {
            "slot-1": { "component_id": "music-control-card" },
            "slot-2": {
              "layout": {
                "container_id": "two-column-container",
                "styles": {
                  "grid-template-columns": "1fr 1fr"
                },
                "slots": {
                  "left-panel": { "component_id": "quick-action-card" },
                  "main-content": { "component_id": "vpa-avatar-widget" }
                }
              }
            }
          }
        }
      }
    }
  }
}
```

**输出格式:** HTML
```

### 示例2：可爱的家庭旅行仪表盘

**目标:** 创建一个有趣、适合多用户查看的界面，包含天气、音乐和趣味信息。

```markdown
**角色:** 你是一位专业的HMI布局设计师。

**任务:** 
1.  根据下方提供的 `场景ID` 和 `风格ID`，理解整体的氛围和用户意图。
2.  严格遵循 `布局` JSON对象定义的结构，将其渲染为完整的HTML代码。
3.  **对于布局中的每一个组件，请根据场景和风格，为其生成贴切、生动的占位内容。** 例如，在`kawaii_family_trip`场景下，音乐卡片不应是默认的“歌曲名”，而应是“卡哇伊小调”或“家庭出游欢快曲”等符合氛围的名称。

**场景:** `kawaii_family_trip`

**风格:** `kawaii`

**布局:**
```json
{
  "layout": {
    "container_id": "three-column-grid-container",
    "slots": {
      "col-1": { "component_id": "weather-card" },
      "col-2": { "component_id": "music-control-card" },
      "col-3": { "component_id": "todo-card" }
    }
  }
}
```

**输出格式:** HTML
```
