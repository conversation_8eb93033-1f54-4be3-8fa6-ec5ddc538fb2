import { skinPresets, useCarSkinStore } from './Store';

export class SkinPicker {
  container: HTMLDivElement;
  activeIndex: number;
  isVisible: boolean;
  containerStyle: Record<string, string>;
  pickerStyle: Record<string, string>;
  skinItemStyle: Record<string, string>;
  activeItemStyle: Record<string, string>;
  toggleButton: HTMLButtonElement;
  
  constructor() {
    this.container = document.createElement('div');
    this.activeIndex = 0;
    this.isVisible = false;
    this.toggleButton = document.createElement('button');
    
    // 容器样式
    this.containerStyle = {
      position: 'fixed',
      bottom: '80px',
      right: '80px',
      zIndex: '10000',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '10px',
      opacity: '0',
      transition: 'opacity 0.3s ease, transform 0.3s ease',
      pointerEvents: 'none',
    };
    
    // 车衣选择器容器样式 - 毛玻璃效果
    this.pickerStyle = {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'stretch',
      gap: '12px',
      padding: '20px',
      backgroundColor: 'rgba(20, 20, 20, 0.5)', // 更透明的背景色
      backdropFilter: 'blur(10px)', // 毛玻璃效果
      WebkitBackdropFilter: 'blur(10px)', // Safari 支持
      borderRadius: '15px',
      width: '220px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)', // 微妙的边框增强毛玻璃感
    };
    
    // 车衣项样式
    this.skinItemStyle = {
      display: 'flex',
      alignItems: 'center',
      padding: '10px',
      borderRadius: '8px',
      cursor: 'pointer',
      transition: 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease',
      boxSizing: 'border-box',
      border: '1px solid transparent',
      backgroundColor: 'rgba(255, 255, 255, 0.05)'
    };
    
    // 激活状态的车衣项样式
    this.activeItemStyle = {
      backgroundColor: 'rgba(38, 214, 233, 0.1)',
      border: '1px solid rgba(38, 214, 233, 0.3)',
      boxShadow: '0 0 15px rgba(38, 214, 233, 0.2)',
    };
    
    this.init();
  }
  
  init() {
    // 应用容器样式
    Object.assign(this.container.style, this.containerStyle);
    
    // 创建标题
    const title = document.createElement('div');
    title.textContent = '选择车衣样式';
    title.style.color = 'white';
    title.style.fontSize = '16px';
    title.style.fontWeight = 'bold';
    title.style.marginBottom = '10px';
    title.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.5)';
    this.container.appendChild(title);
    
    // 创建车衣选择器容器
    const pickerContainer = document.createElement('div');
    Object.assign(pickerContainer.style, this.pickerStyle);
    
    // 添加webkit前缀
    (pickerContainer.style as any)['-webkit-backdrop-filter'] = 'blur(10px)';
    
    // 创建车衣选项
    skinPresets.forEach((preset, index) => {
      const skinItem = document.createElement('div');
      Object.assign(skinItem.style, this.skinItemStyle);
      
      // 创建内容容器
      const itemContent = document.createElement('div');
      itemContent.style.display = 'flex';
      itemContent.style.alignItems = 'center';
      itemContent.style.width = '100%';
      
      // 创建缩略图/颜色块
      const thumbnail = document.createElement('div');
      thumbnail.style.width = '40px';
      thumbnail.style.height = '40px';
      thumbnail.style.borderRadius = '6px';
      thumbnail.style.marginRight = '12px';
      thumbnail.style.border = '1px solid rgba(255, 255, 255, 0.1)';
      thumbnail.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
      
      // 如果是默认选项，显示颜色块
      if (preset.id === null) {
        // 默认显示纯色块
        thumbnail.style.backgroundImage = 'linear-gradient(135deg, #26d6e9, #0a94a4)';
      } else {
        // 使用缩略图，可以用实际的图片或者临时使用颜色表示
        thumbnail.style.backgroundColor = '#607d8b';
        // 如果有实际缩略图，可以设置背景图片
        thumbnail.style.backgroundImage = "url('./texture/" + preset.id + "_thumb.jpg')";
        thumbnail.style.backgroundSize = 'cover';
        thumbnail.style.backgroundPosition = 'center';
      }
      
      // 创建文本信息区域
      const textInfo = document.createElement('div');
      
      // 创建标题
      const itemTitle = document.createElement('div');
      itemTitle.textContent = preset.name;
      itemTitle.style.color = 'white';
      itemTitle.style.fontSize = '14px';
      itemTitle.style.fontWeight = 'bold';
      
      // 创建描述
      const itemDescription = document.createElement('div');
      itemDescription.textContent = preset.description;
      itemDescription.style.color = 'rgba(255, 255, 255, 0.7)';
      itemDescription.style.fontSize = '12px';
      
      // 添加标题和描述到文本区域
      textInfo.appendChild(itemTitle);
      textInfo.appendChild(itemDescription);
      
      // 组装项目内容
      itemContent.appendChild(thumbnail);
      itemContent.appendChild(textInfo);
      skinItem.appendChild(itemContent);
      
      // 设置数据属性
      skinItem.dataset.skinId = preset.id || 'null';
      skinItem.dataset.index = String(index);
      
      // 激活默认车衣
      if (index === this.activeIndex) {
        Object.assign(skinItem.style, this.activeItemStyle);
      }
      
      // 悬停效果
      skinItem.addEventListener('mouseenter', () => {
        if (index !== this.activeIndex) {
          skinItem.style.transform = 'translateY(-2px)';
          skinItem.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.2)';
          skinItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        }
      });
      
      skinItem.addEventListener('mouseleave', () => {
        if (index !== this.activeIndex) {
          skinItem.style.transform = 'translateY(0)';
          skinItem.style.boxShadow = 'none';
          skinItem.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
        }
      });
      
      // 点击事件
      skinItem.addEventListener('click', () => {
        this.selectSkin(index);
      });
      
      pickerContainer.appendChild(skinItem);
    });
    
    this.container.appendChild(pickerContainer);
    
    // 添加到文档
    document.body.appendChild(this.container);
    
    // 添加切换按钮
    this.addToggleButton();
    
    // 确保面板初始状态为隐藏
    this.container.style.opacity = '0';
    this.container.style.pointerEvents = 'none';
    this.container.style.transform = 'translateY(20px)';
    
    // 确保车衣选择器初始状态正确
    const currentSkin = useCarSkinStore.getState().currentSkin;
    const initialIndex = skinPresets.findIndex(preset => 
      preset.id === currentSkin || 
      (preset.id === null && currentSkin === null)
    );
    if (initialIndex !== -1) {
      this.selectSkin(initialIndex);
    }
    
    // 添加到控制台，方便调试
    console.log('Skin Picker initialized');
    (window as any).skinPicker = this;
  }
  
  selectSkin(index: number) {
    // 更新激活状态
    const skinItems = this.container.querySelectorAll('[data-skin-id]');
    skinItems.forEach((item, i) => {
      const skinItem = item as HTMLDivElement;
      if (i === index) {
        Object.assign(skinItem.style, {...this.skinItemStyle, ...this.activeItemStyle});
      } else {
        Object.assign(skinItem.style, this.skinItemStyle);
      }
    });
    
    this.activeIndex = index;
    
    // 更新全局状态
    const selectedPreset = skinPresets[index];
    const { setSkin } = useCarSkinStore.getState();
    setSkin(selectedPreset.id);
    
    // 添加特效 - 闪光效果
    this.addSelectEffect(skinItems[index] as HTMLDivElement);
    
    console.log(`Skin changed to: ${selectedPreset.name}`);
  }
  
  // 添加选择特效
  addSelectEffect(element: HTMLDivElement) {
    // 创建闪光覆盖层
    const flash = document.createElement('div');
    flash.style.position = 'absolute';
    flash.style.top = '0';
    flash.style.left = '0';
    flash.style.width = '100%';
    flash.style.height = '100%';
    flash.style.borderRadius = '8px';
    flash.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
    flash.style.pointerEvents = 'none';
    flash.style.zIndex = '1000';
    flash.style.opacity = '0';
    
    // 覆盖层上的径向渐变
    flash.style.background = 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%)';
    
    // 根据元素位置设置覆盖层
    element.style.position = 'relative';
    element.appendChild(flash);
    
    // 动画：闪光效果
    setTimeout(() => {
      flash.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
      flash.style.opacity = '1';
      flash.style.transform = 'scale(1.5)';
      
      setTimeout(() => {
        flash.style.opacity = '0';
        
        // 动画完成后移除闪光元素
        setTimeout(() => {
          if (flash.parentNode === element) {
            element.removeChild(flash);
          }
        }, 600);
      }, 300);
    }, 0);
  }
  
  addToggleButton() {
    const button = this.toggleButton;
    button.textContent = '车衣风格';
    
    const buttonStyles = {
      position: 'fixed',
      bottom: '20px',
      right: '150px', // 位置调整，与色彩选择器错开
      padding: '12px 20px',
      backgroundColor: 'rgba(20, 20, 20, 0.7)',
      backdropFilter: 'blur(5px)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '30px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: 'bold',
      zIndex: '10000',
      boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
      transition: 'transform 0.2s ease, background-color 0.2s ease',
    };
    
    Object.assign(button.style, buttonStyles);
    // 添加webkit前缀
    (button.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
    
    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = 'rgba(30, 30, 30, 0.8)';
      button.style.transform = 'translateY(-2px)';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
      button.style.transform = 'translateY(0)';
    });
    
    button.addEventListener('click', () => {
      this.toggleVisibility();
    });
    
    document.body.appendChild(button);
    
    // 确保按钮可见
    console.log('Skin toggle button added to the DOM');
  }
  
  toggleVisibility() {
    this.isVisible = !this.isVisible;
    
    if (this.isVisible) {
      this.container.style.opacity = '1';
      this.container.style.pointerEvents = 'all';
      this.container.style.transform = 'translateY(0)';
      this.toggleButton.style.backgroundColor = 'rgba(38, 214, 233, 0.7)';
      this.toggleButton.style.backdropFilter = 'blur(5px)';
      (this.toggleButton.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
      this.toggleButton.style.border = '1px solid rgba(255, 255, 255, 0.2)';
    } else {
      this.container.style.opacity = '0';
      this.container.style.pointerEvents = 'none';
      this.container.style.transform = 'translateY(20px)';
      this.toggleButton.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
      this.toggleButton.style.backdropFilter = 'blur(5px)';
      (this.toggleButton.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
      this.toggleButton.style.border = '1px solid rgba(255, 255, 255, 0.1)';
    }
    
    console.log(`Skin picker visibility: ${this.isVisible}`);
  }
  
  // 销毁函数
  dispose() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    if (this.toggleButton && this.toggleButton.parentNode) {
      this.toggleButton.parentNode.removeChild(this.toggleButton);
    }
  }
}
