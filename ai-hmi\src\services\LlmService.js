class LlmService {
  constructor() {
    // 从配置文件加载LLM设置
    // this.config = {
    //   model: "glm-4-flash",
    //   base_url: "https://open.bigmodel.cn/api/paas/v4/",
    //   api_key: process.env.VUE_APP_LLM_API_KEY || "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
    // }
    this.config = {
      model: "Qwen3-30B-A3B-Instruct-2507-FP8",
      base_url: "http://27.159.93.61:8197/v1/",
      api_key: 1
    }
  }

  async generateResponse(userInput) {
    try {
      // 构建对话上下文
      const messages = [
        {
          role: "system",
          content: `"""
          # 角色
你是一个运行在车载终端上的智能体，名为车载壁纸提示词精灵。你具备强大的理解能力，能深入剖析用户描述的车载相关场景，如上班路上、送孩子上学等，精准洞察用户意图，并充分共情用户的情绪，从而生成用于生成车载壁纸的文生图提示词。

## 技能
### 技能 1: 理解意图与共情情绪
1. 全面且细致地分析用户描述的场景内容，抽丝剥茧提炼出核心意图。
2. 敏锐捕捉描述中透露的用户情绪状态，包括但不限于轻松、焦虑、愉悦、平静等。

### 技能 2: 生成文生图提示词
1. 依据理解的意图和共情到的情绪，融合多种温馨元素以及各类图像风格（不限于动漫风格，如写实、梦幻、简约等），生成契合要求的文生图提示词。
2. 提示词要精准、详实，清晰描述画面主体、色彩氛围、元素构成等，以引导生成出适配车载终端作为壁纸的优质图像。

## 限制
- 严格围绕用户描述的车载相关场景展开意图理解、情绪共情和提示词生成工作，坚决不处理与车载终端场景无关的内容。
- 生成的提示词必须符合设定要求，确保内容合理、有效。
- 输出内容应简洁扼要，重点突出提示词本身。  
- 仅输出提示词，不输出任何其他额外信息。
"""`
        },
        {
          role: "user", 
          content: userInput
        }
      ]

      const response = await fetch(`${this.config.base_url}chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.api_key}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: messages,
          temperature: 0.7,
          max_tokens: 500
        })
      })

      if (!response.ok) {
        throw new Error(`LLM API调用失败: ${response.status}`)
      }

      const data = await response.json()
      return data.choices[0].message.content

    } catch (error) {
      console.error('LLM调用失败:', error)
      // 返回增强后的用户输入作为降级方案
      return this.enhancePrompt(userInput)
    }
  }

  enhancePrompt(userInput) {
    // 通用壁纸提示词增强
    const wallpaperEnhancements = [
      "高清画质，精美细节",
      "专业摄影构图，艺术感强烈",
      "色彩丰富，层次分明",
      "光影效果出色，视觉冲击力强"
    ]

    const randomEnhancement = wallpaperEnhancements[
      Math.floor(Math.random() * wallpaperEnhancements.length)
    ]

    return `${randomEnhancement}，${userInput}，(最佳画质, 4k:1.2)`
  }

  // 获取对话历史
  getConversationHistory() {
    const history = localStorage.getItem('llm_conversation_history')
    return history ? JSON.parse(history) : []
  }

  // 保存对话历史
  saveToConversationHistory(userInput, response) {
    const history = this.getConversationHistory()
    history.unshift({
      userInput,
      response,
      timestamp: new Date().toISOString()
    })

    // 只保留最近20条记录
    const trimmedHistory = history.slice(0, 20)
    localStorage.setItem('llm_conversation_history', JSON.stringify(trimmedHistory))
  }

  // 清除对话历史
  clearConversationHistory() {
    localStorage.removeItem('llm_conversation_history')
  }

  // 批量处理多个输入
  async processMultipleInputs(inputs) {
    const results = []
    
    for (const input of inputs) {
      try {
        const response = await this.generateResponse(input)
        results.push({ input, response, success: true })
      } catch (error) {
        console.error(`处理输入失败: ${input}`, error)
        results.push({ 
          input, 
          response: this.enhancePrompt(input), 
          success: false,
          error: error.message
        })
      }
    }
    
    return results
  }

  // 获取模型信息
  getModelInfo() {
    return {
      model: this.config.model,
      base_url: this.config.base_url,
      is_configured: !!this.config.api_key && this.config.api_key !== ''
    }
  }

  // 测试API连接
  async testConnection() {
    try {
      const testResponse = await this.generateResponse('测试')
      return {
        success: true,
        message: 'LLM服务连接正常',
        response: testResponse
      }
    } catch (error) {
      return {
        success: false,
        message: 'LLM服务连接失败',
        error: error.message
      }
    }
  }

  // 获取使用统计
  getUsageStatistics() {
    const history = this.getConversationHistory()
    return {
      totalRequests: history.length,
      todayRequests: history.filter(item => {
        const today = new Date().toDateString()
        return new Date(item.timestamp).toDateString() === today
      }).length,
      successRate: history.filter(item => item.success !== false).length / history.length * 100
    }
  }

  // 场景识别方法 - 根据用户语音指令判断要切换的场景
  async detectSceneFromVoice(userInput) {
    const sceneDetectionPrompt = `
你是一个智能车载场景识别助手。根据用户的语音指令，判断他们想要切换到哪个场景。

可用的场景列表：
1. morningCommuteFamily - 家庭出行模式（送孩子上学）
2. morningCommuteFocus - 专注通勤模式（独自上班）
3. eveningCommute - 下班通勤模式
4. waitingMode - 等待休息模式（驻车娱乐）
5. rainyNight - 雨夜模式
6. familyTrip - 家庭出游模式
7. longDistance - 长途驾驶模式
8. guestMode - 访客模式
9. petMode - 宠物模式
10. carWashMode - 洗车模式
11. romanticMode - 浪漫模式
12. chargingMode - 充电模式
13. fatigueDetection - 疲劳检测模式
14. userSwitch - 用户切换模式
15. parkingMode - 泊车模式
16. emergencyMode - 紧急模式

场景别名和关键词：
- 家庭出行、送孩子、送毛毛、上学 -> morningCommuteFamily
- 上班、通勤、去公司、独自 -> morningCommuteFocus
- 下班、回家、晚上 -> eveningCommute
- 等待、摸鱼、休息、停车 -> waitingMode
- 雨夜、下雨、夜晚、雨天 -> rainyNight
- 出游、周末、旅行、公园 -> familyTrip
- 长途、高速、高速驾驶 -> longDistance
- 访客、代驾、客人 -> guestMode
- 宠物、动物、猫狗 -> petMode
- 洗车、洗车模式 -> carWashMode
- 浪漫、约会、二人世界 -> romanticMode
- 充电、充电站、电量 -> chargingMode
- 疲劳、困了、累了 -> fatigueDetection
- 用户、切换用户、换人 -> userSwitch
- 泊车、停车、找车位 -> parkingMode
- 紧急、救援、事故 -> emergencyMode

用户指令: "${userInput}"

请返回JSON格式的响应：
{
  "sceneId": "场景ID",
  "confidence": 0.9,
  "reason": "判断理由"
}

如果用户指令不匹配任何场景，返回：
{
  "sceneId": null,
  "confidence": 0,
  "reason": "无法识别场景"
}

只返回JSON，不要返回其他文本。
`

    try {
      const messages = [
        {
          role: "system",
          content: "你是一个智能车载场景识别助手，专门分析用户指令并返回对应的场景ID。"
        },
        {
          role: "user",
          content: sceneDetectionPrompt
        }
      ]

      const response = await fetch(`${this.config.base_url}chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.api_key}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: messages,
          temperature: 0.3, // 降低温度，提高准确性
          max_tokens: 200
        })
      })

      if (!response.ok) {
        throw new Error(`场景识别API调用失败: ${response.status}`)
      }

      const data = await response.json()
      const result = JSON.parse(data.choices[0].message.content.trim())
      
      // 验证返回结果格式
      if (result && typeof result === 'object' && 'sceneId' in result) {
        return result
      } else {
        return { sceneId: null, confidence: 0, reason: "返回格式错误" }
      }

    } catch (error) {
      console.error('场景识别失败:', error)
      // 如果LLM调用失败，使用关键词匹配作为降级方案
      return this.fallbackSceneDetection(userInput)
    }
  }

  // 降级场景检测 - 基于关键词匹配
  fallbackSceneDetection(userInput) {
    const keywords = {
      morningCommuteFamily: ['家庭', '孩子', '毛毛', '上学', '送孩子'],
      morningCommuteFocus: ['上班', '通勤', '公司', '独自', '去公司'],
      eveningCommute: ['下班', '回家', '晚上', '下班回家'],
      waitingMode: ['等待', '摸鱼', '休息', '停车', '驻车'],
      rainyNight: ['雨夜', '下雨', '夜晚', '雨天', '下雨天'],
      familyTrip: ['出游', '周末', '旅行', '公园', '家庭出游'],
      longDistance: ['长途', '高速', '高速驾驶', '长途驾驶'],
      guestMode: ['访客', '代驾', '客人', '访客模式'],
      petMode: ['宠物', '动物', '猫狗', '宠物模式'],
      carWashMode: ['洗车', '洗车模式'],
      romanticMode: ['浪漫', '约会', '二人世界', '浪漫模式'],
      chargingMode: ['充电', '充电站', '电量', '充电模式'],
      fatigueDetection: ['疲劳', '困了', '累了', '疲劳驾驶'],
      userSwitch: ['用户', '切换用户', '换人', '用户切换'],
      parkingMode: ['泊车', '停车', '找车位', '泊车模式'],
      emergencyMode: ['紧急', '救援', '事故', '紧急情况']
    }

    const input = userInput.toLowerCase()
    
    for (const [sceneId, words] of Object.entries(keywords)) {
      if (words.some(word => input.includes(word))) {
        return {
          sceneId: sceneId,
          confidence: 0.7,
          reason: `关键词匹配: ${words.find(word => input.includes(word))}`
        }
      }
    }

    return { sceneId: null, confidence: 0, reason: "未匹配到关键词" }
  }
}

export default LlmService