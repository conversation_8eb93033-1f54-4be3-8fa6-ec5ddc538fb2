<template>
  <div class="news-digest-card" :style="cardStyle">
    <div class="card-header">
      <div class="header-icon">
        <i class="fas fa-newspaper"></i>
      </div>
      <h3 class="card-title">{{ title }}</h3>
      <div class="refresh-btn" @click="refreshNews">
        <i class="fas fa-sync-alt" :class="{ spinning: isRefreshing }"></i>
      </div>
    </div>
    <div class="news-content">
      <div class="featured-news" v-if="featuredNews">
        <div class="news-image">
          <img :src="featuredNews.image" :alt="featuredNews.title" />
          <div class="news-category">{{ featuredNews.category }}</div>
        </div>
        <div class="news-info">
          <h4 class="news-title">{{ featuredNews.title }}</h4>
          <p class="news-summary">{{ featuredNews.summary }}</p>
          <div class="news-meta">
            <span class="news-source">
              <i class="fas fa-globe"></i>
              {{ featuredNews.source }}
            </span>
            <span class="news-time">
              <i class="fas fa-clock"></i>
              {{ featuredNews.time }}
            </span>
          </div>
        </div>
      </div>
      <div class="news-list">
        <div 
          v-for="(news, index) in newsList" 
          :key="index" 
          class="news-item"
          @click="selectNews(news)"
        >
          <div class="news-thumbnail">
            <img :src="news.image" :alt="news.title" />
          </div>
          <div class="news-details">
            <h5 class="news-item-title">{{ news.title }}</h5>
            <div class="news-item-meta">
              <span class="news-item-source">{{ news.source }}</span>
              <span class="news-item-time">{{ news.time }}</span>
            </div>
          </div>
          <div class="news-indicator" v-if="news.isNew">
            <div class="new-badge">新</div>
          </div>
        </div>
      </div>
      <div class="news-actions">
        <button class="btn btn-primary" @click="viewAllNews">
          <i class="fas fa-list"></i>
          查看全部
        </button>
        <button class="btn btn-secondary" @click="customizeNews">
          <i class="fas fa-cog"></i>
          定制
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NewsDigestCard',
  props: {
    title: {
      type: String,
      default: '新闻摘要'
    },
    featured: {
      type: Object,
      default: () => ({
        title: '科技创新推动未来发展',
        summary: '最新科技趋势和创新成果正在改变我们的生活方式...',
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMTAwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjgwIiByeD0iOCIgZmlsbD0iIzM3NDE1MSIvPgo8cmVjdCB4PSIxMCIgeT0iMTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI0IiByeD0iMiIgZmlsbD0iI0ZGRkZGRiIvPgo8cmVjdCB4PSIxMCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSIzIiByeD0iMS41IiBmaWxsPSIjOUNBM0FGIi8+CjxyZWN0IHg9IjEwIiB5PSIzMCIgd2lkdGg9IjcwIiBoZWlnaHQ9IjMiIHJ4PSIxLjUiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2Zz4K',
        category: '科技',
        source: '科技日报',
        time: '2小时前'
      })
    },
    news: {
      type: Array,
      default: () => [
        {
          title: '新能源汽车销量创新高',
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iNCIgZmlsbD0iIzRDQUY1MCIvPgo8cGF0aCBkPSJNMTIgMjBMMTggMjZMMjggMTQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=',
          source: '财经网',
          time: '1小时前',
          isNew: true
        },
        {
          title: '人工智能在医疗领域的应用',
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iNCIgZmlsbD0iIzNCODJGNiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiLz4KPHN2Zz4K',
          source: '健康时报',
          time: '3小时前',
          isNew: false
        },
        {
          title: '气候变化对全球经济的影响',
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iNCIgZmlsbD0iI0ZGOEI0QSIvPgo8cGF0aCBkPSJNMjAgMTBWMzBNMTAgMjBIMzAiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=',
          source: '环球时报',
          time: '5小时前',
          isNew: false
        }
      ]
    },
    themeColors: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isRefreshing: false
    }
  },
  computed: {
    featuredNews() {
      return this.featured
    },
    newsList() {
      return this.news
    },
    cardStyle() {
      if (this.themeColors) {
        return {
          background: `linear-gradient(135deg, ${this.themeColors.primary}15, ${this.themeColors.secondary}15)`,
          borderColor: this.themeColors.accent
        }
      }
      return {}
    }
  },
  methods: {
    refreshNews() {
      this.isRefreshing = true
      this.$emit('refresh-news')
      setTimeout(() => {
        this.isRefreshing = false
      }, 1000)
    },
    selectNews(news) {
      this.$emit('select-news', news)
    },
    viewAllNews() {
      this.$emit('view-all-news')
    },
    customizeNews() {
      this.$emit('customize-news')
    }
  }
}
</script>

<style scoped>
.news-digest-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  color: white;
  font-family: 'Arial', sans-serif;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #6366F1;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.refresh-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.refresh-btn i {
  font-size: 12px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.featured-news {
  display: flex;
  gap: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.news-image {
  position: relative;
  width: 100px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-category {
  position: absolute;
  top: 6px;
  left: 6px;
  background: rgba(99, 102, 241, 0.9);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.news-info {
  flex: 1;
}

.news-title {
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
}

.news-summary {
  margin: 0 0 8px 0;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.news-source,
.news-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.news-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.news-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.news-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.news-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.news-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-details {
  flex: 1;
  min-width: 0;
}

.news-item-title {
  margin: 0 0 4px 0;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-item-meta {
  display: flex;
  gap: 8px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.news-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
}

.new-badge {
  background: #EF4444;
  color: white;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 8px;
  font-weight: 600;
}

.news-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
  padding-top: 12px;
}

.btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #6366F1, #4F46E5);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4F46E5, #4338CA);
  transform: translateY(-1px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.btn i {
  font-size: 10px;
}

/* 滚动条样式 */
.news-list::-webkit-scrollbar {
  width: 4px;
}

.news-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.news-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.news-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>