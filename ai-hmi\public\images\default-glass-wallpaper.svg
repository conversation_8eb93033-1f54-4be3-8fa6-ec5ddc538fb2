<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="glassBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:0.6" />
    </linearGradient>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
    </filter>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="100%" height="100%" fill="url(#glassBg)"/>
  
  <!-- 玻璃拟态装饰元素 -->
  <circle cx="300" cy="200" r="150" fill="rgba(255,255,255,0.1)" filter="url(#blur)"/>
  <circle cx="1600" cy="300" r="200" fill="rgba(255,255,255,0.08)" filter="url(#blur)"/>
  <circle cx="800" cy="800" r="180" fill="rgba(255,255,255,0.12)" filter="url(#blur)"/>
  <circle cx="1400" cy="900" r="120" fill="rgba(255,255,255,0.06)" filter="url(#blur)"/>
  
  <!-- 几何装饰 -->
  <polygon points="100,100 200,50 300,100 250,200 150,200" fill="rgba(255,255,255,0.05)" filter="url(#blur)"/>
  <polygon points="1500,700 1650,650 1750,750 1650,850 1550,800" fill="rgba(255,255,255,0.04)" filter="url(#blur)"/>
</svg>