[{"D:\\code\\pythonWork\\theme\\ai-hmi\\src\\main.js": "1", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue": "2", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassThemeManager.vue": "3", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue": "4", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue": "5", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js": "6", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\LlmService.js": "7", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\AsrService.js": "8", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\TtsService.js": "9", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js": "10", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\glassStyleUtils.js": "11", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue": "12", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\SceneManager.js": "13", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue": "14", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue": "15", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue": "16", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue": "17", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js": "18", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\SceneContextManager.js": "19", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js": "20", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js": "21"}, {"size": 90, "mtime": 1753845085034, "results": "22", "hashOfConfig": "23"}, {"size": 5387, "mtime": 1753957630545, "results": "24", "hashOfConfig": "23"}, {"size": 7814, "mtime": 1753849317859, "results": "25", "hashOfConfig": "23"}, {"size": 11595, "mtime": 1753927909625, "results": "26", "hashOfConfig": "23"}, {"size": 6069, "mtime": 1753924229010, "results": "27", "hashOfConfig": "23"}, {"size": 9102, "mtime": 1754008893564, "results": "28", "hashOfConfig": "23"}, {"size": 11085, "mtime": 1754017652374, "results": "29", "hashOfConfig": "23"}, {"size": 1617, "mtime": 1753850270876, "results": "30", "hashOfConfig": "23"}, {"size": 4327, "mtime": 1753857066008, "results": "31", "hashOfConfig": "23"}, {"size": 15105, "mtime": 1753924229023, "results": "32", "hashOfConfig": "23"}, {"size": 3918, "mtime": 1753850283521, "results": "33", "hashOfConfig": "23"}, {"size": 38426, "mtime": 1754015192665, "results": "34", "hashOfConfig": "23"}, {"size": 14619, "mtime": 1754015192668, "results": "35", "hashOfConfig": "23"}, {"size": 12769, "mtime": 1753924229010, "results": "36", "hashOfConfig": "23"}, {"size": 76365, "mtime": 1754017652373, "results": "37", "hashOfConfig": "23"}, {"size": 6347, "mtime": 1753952377657, "results": "38", "hashOfConfig": "23"}, {"size": 20551, "mtime": 1754008893561, "results": "39", "hashOfConfig": "23"}, {"size": 12102, "mtime": 1753924229023, "results": "40", "hashOfConfig": "23"}, {"size": 15166, "mtime": 1753950309312, "results": "41", "hashOfConfig": "23"}, {"size": 12006, "mtime": 1753952352571, "results": "42", "hashOfConfig": "23"}, {"size": 8519, "mtime": 1754008893564, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, "17wmy<PERSON>", {"filePath": "47", "messages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "52"}, {"filePath": "53", "messages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "55", "messages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "57", "messages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "59", "messages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "63", "messages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "65", "messages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "67", "messages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\main.js", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassThemeManager.vue", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\LlmService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\AsrService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\TtsService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\glassStyleUtils.js", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\SceneManager.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\SceneContextManager.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js", []]