class TtsService {
  constructor() {
    // 使用系统自带的TTS服务
    this.synthesis = window.speechSynthesis
    this.isSupported = 'speechSynthesis' in window
    this.currentUtterance = null
    this.retryCount = 0
    this.maxRetries = 3
    
    // 等待语音列表加载完成
    this.waitForVoices()
  }

  // 等待语音列表加载完成
  waitForVoices() {
    if (this.synthesis.getVoices().length === 0) {
      // 在Chrome中，语音列表是异步加载的
      this.synthesis.onvoiceschanged = () => {
        console.log('语音列表加载完成，共', this.synthesis.getVoices().length, '个语音')
      }
    }
  }

  async speak(text, options = {}) {
    if (!this.isSupported) {
      console.warn('浏览器不支持语音合成')
      return
    }

    // 停止当前播放
    this.synthesis.cancel()
    this.currentUtterance = null
    this.retryCount = 0

    return this.speakWithRetry(text, options)
  }

  async speakWithRetry(text, options = {}) {
    const utterance = new SpeechSynthesisUtterance(text)
    
    // 设置语音参数
    utterance.lang = options.lang || 'zh-CN'
    utterance.rate = options.rate || 1.0
    utterance.pitch = options.pitch || 1.0
    utterance.volume = options.volume || 1.0

    // 选择中文语音
    const voices = this.synthesis.getVoices()
    const chineseVoice = voices.find(voice => 
      voice.lang.includes('zh') || voice.name.includes('Chinese')
    )
    
    if (chineseVoice) {
      utterance.voice = chineseVoice
      console.log('使用中文语音:', chineseVoice.name)
    } else {
      console.warn('未找到中文语音，使用默认语音')
    }

    this.currentUtterance = utterance

    return new Promise((resolve, reject) => {
      utterance.onend = () => {
        this.currentUtterance = null
        this.retryCount = 0
        resolve()
      }

      utterance.onerror = async (event) => {
        console.error('语音合成错误:', event.error)
        this.currentUtterance = null
        
        // 处理中断错误，尝试重试
        if (event.error === 'interrupted' && this.retryCount < this.maxRetries) {
          this.retryCount++
          console.log(`语音合成被中断，第${this.retryCount}次重试...`)
          
          // 延迟后重试
          setTimeout(async () => {
            try {
              await this.speakWithRetry(text, options)
              resolve()
            } catch (error) {
              reject(error)
            }
          }, 500 * this.retryCount) // 递增延迟
        } else {
          reject(new Error(`语音合成失败: ${event.error}`))
        }
      }

      try {
        this.synthesis.speak(utterance)
      } catch (error) {
        console.error('语音合成异常:', error)
        reject(new Error(`语音合成失败: ${error.message}`))
      }
    })
  }

  stop() {
    if (this.synthesis) {
      this.synthesis.cancel()
      this.currentUtterance = null
      this.retryCount = 0
    }
  }

  getVoices() {
    return this.synthesis.getVoices()
  }

  // 获取可用的中文语音
  getChineseVoices() {
    const voices = this.getVoices()
    return voices.filter(voice => 
      voice.lang.includes('zh') || 
      voice.name.includes('Chinese') ||
      voice.name.includes('中文')
    )
  }

  // 设置默认语音
  setDefaultVoice(voiceName) {
    const voices = this.getVoices()
    const voice = voices.find(v => v.name === voiceName)
    if (voice) {
      this.defaultVoice = voice
    }
  }

  // 检查是否正在播放
  get isSpeaking() {
    return this.synthesis.speaking
  }

  // 检查是否暂停
  get isPaused() {
    return this.synthesis.paused
  }

  // 暂停播放
  pause() {
    if (this.synthesis) {
      this.synthesis.pause()
    }
  }

  // 恢复播放
  resume() {
    if (this.synthesis) {
      this.synthesis.resume()
    }
  }

  // 批量播放文本
  async speakTexts(texts, options = {}) {
    for (const text of texts) {
      await this.speak(text, options)
    }
  }

  // 获取语音状态
  getStatus() {
    return {
      isSupported: this.isSupported,
      isSpeaking: this.isSpeaking,
      isPaused: this.isPaused,
      voices: this.getVoices().length,
      chineseVoices: this.getChineseVoices().length
    }
  }
}

export default TtsService