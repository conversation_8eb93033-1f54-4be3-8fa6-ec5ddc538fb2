<template>
  <div class="glass-theme-manager" :style="themeStyles">
    <!-- 玻璃态卡片容器 -->
    <div class="glass-cards-container">
      <!-- 导航卡片 -->
      <GlassCard 
        title="导航"
        :icon="'fas fa-route'"
        :style="glassStyles"
      >
        <div class="navigation-content">
          <div class="destination">前往公司</div>
          <div class="route-info">
            <span class="distance">12.5 公里</span>
            <span class="duration">25 分钟</span>
          </div>
          <div class="route-preview">
            <img src="/assets/map-preview.jpg" alt="路线预览">
          </div>
        </div>
      </GlassCard>

      <!-- 音乐控制卡片 -->
      <GlassCard 
        title="音乐"
        :icon="'fas fa-music'"
        :style="glassStyles"
      >
        <div class="music-content">
          <div class="song-info">
            <div class="song-title">Morning Coffee</div>
            <div class="artist">Jazz Collective</div>
          </div>
          <div class="music-controls">
            <button class="control-btn"><i class="fas fa-backward"></i></button>
            <button class="control-btn play-pause"><i class="fas fa-play"></i></button>
            <button class="control-btn"><i class="fas fa-forward"></i></button>
          </div>
        </div>
      </GlassCard>

      <!-- 待办事项卡片 -->
      <GlassCard 
        title="今日待办"
        :icon="'fas fa-tasks'"
        :style="glassStyles"
      >
        <div class="todo-content">
          <ul class="todo-list">
            <li class="todo-item">
              <input type="checkbox" id="todo1">
              <label for="todo1">团队会议 10:00</label>
            </li>
            <li class="todo-item">
              <input type="checkbox" id="todo2">
              <label for="todo2">项目汇报 14:00</label>
            </li>
            <li class="todo-item">
              <input type="checkbox" id="todo3">
              <label for="todo3">客户电话 16:30</label>
            </li>
          </ul>
        </div>
      </GlassCard>
    </div>

    <!-- VPA助手 -->
    <div class="vpa-assistant" :style="vpaStyles">
      <div class="vpa-avatar">🤖</div>
      <div class="vpa-message">
        早上好！今天天气不错，祝您一路平安！
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <i class="fas fa-spinner fa-spin"></i>
        <span>正在生成壁纸...</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import ImageGenerationService from '@/services/ImageGenerationService'
import ColorExtractor from '@/utils/ColorExtractor'
import GlassStyleUtils from '@/utils/glassStyleUtils'
import GlassCard from './GlassCard.vue'

export default {
  name: 'GlassThemeManager',
  components: {
    GlassCard
  },

  props: {
    userInput: {
      type: String,
      default: '现代玻璃建筑，简洁商务风格'
    }
  },

  setup(props) {
    const currentWallpaper = ref('')
    const currentColors = ref({})
    const glassStyles = ref({})
    const isLoading = ref(false)

    const themeStyles = computed(() => {
      return {
        backgroundImage: currentWallpaper.value ? 
          `url(${currentWallpaper.value})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    })

    const vpaStyles = computed(() => {
      return {
        ...glassStyles.value,
        background: currentColors.value.glassBackground || 'rgba(255, 255, 255, 0.15)',
        border: `1px solid ${currentColors.value.glassBorder || 'rgba(255, 255, 255, 0.2)'}`
      }
    })

    const generateTheme = async (userInput) => {
      isLoading.value = true
      
      try {
        // 1. 生成壁纸
        const imageService = new ImageGenerationService()
        const wallpaperResult = await imageService.generateWallpaper(userInput)
        
        currentWallpaper.value = wallpaperResult.imageUrl
        
        // 2. 提取颜色
        const colors = await ColorExtractor.extractColors(wallpaperResult.imageUrl)
        currentColors.value = colors
        
        // 3. 计算玻璃态样式
        const styles = GlassStyleUtils.calculateGlassStyles(colors)
        glassStyles.value = styles
        
        console.log('主题生成完成:', { wallpaperResult, colors, styles })
        
      } catch (error) {
        console.error('主题生成失败:', error)
        // 使用默认样式
        currentColors.value = ColorExtractor.getDefaultGlassmorphismColors()
        glassStyles.value = GlassStyleUtils.calculateGlassStyles(currentColors.value)
      } finally {
        isLoading.value = false
      }
    }

    // 监听用户输入变化
    watch(() => props.userInput, (newInput) => {
      if (newInput && newInput.trim()) {
        generateTheme(newInput)
      }
    })

    onMounted(() => {
      generateTheme(props.userInput)
    })

    return {
      currentWallpaper,
      currentColors,
      glassStyles,
      themeStyles,
      vpaStyles,
      isLoading,
      generateTheme
    }
  }
}
</script>

<style scoped>
.glass-theme-manager {
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.glass-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: calc(100vh - 140px);
  max-width: 1200px;
  margin: 0 auto;
}

.vpa-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  border-radius: 25px;
  min-width: 200px;
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.vpa-avatar {
  font-size: 24px;
}

.vpa-message {
  font-size: 14px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 18px;
}

.loading-content i {
  font-size: 32px;
}

/* 卡片内容样式 */
.navigation-content,
.music-content,
.todo-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.destination {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.route-info {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.route-preview img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.song-info {
  text-align: center;
  margin-bottom: 15px;
}

.song-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.artist {
  font-size: 14px;
  opacity: 0.8;
}

.music-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn.play-pause {
  width: 50px;
  height: 50px;
  font-size: 18px;
}

.todo-list {
  list-style: none;
  padding: 0;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.todo-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.todo-item label {
  font-size: 14px;
  cursor: pointer;
}
</style>