<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI - Apple Style Prototype</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'apple-gray': '#1c1c1e',
                        'apple-light': '#2c2c2e',
                        'apple-blue': '#007aff',
                        'apple-green': '#30d158',
                        'apple-orange': '#ff9500',
                        'apple-red': '#ff3b30'
                    },
                    backdropBlur: {
                        'apple': '20px'
                    },
                    fontFamily: {
                        'sf': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            overflow: hidden;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .vpa-container {
            background: transparent;
            border: none;
        }
        .dynamic-island {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
        }
        .grid-16x9 {
            display: grid;
            grid-template-columns: repeat(16, 1fr);
            grid-template-rows: repeat(9, 1fr);
            gap: 12px;
            height: 100vh;
            padding: 20px;
        }
        .vpa-small {
            grid-column: span 4;
            grid-row: span 2;
        }
        .vpa-panel {
            grid-column: span 8;
            grid-row: span 9;
        }
        .dynamic-island {
            grid-column: span 16;
            grid-row: span 1;
        }
        .standard-card {
            grid-column: span 8;
            grid-row: span 4;
        }
        .large-card {
            grid-column: span 8;
            grid-row: span 9;
        }
        .quick-action {
            grid-column: span 4;
            grid-row: span 2;
        }
        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white font-sf">
    <!-- Background Image -->
    <div class="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80" 
             alt="Background" class="w-full h-full object-cover opacity-30">
    </div>

    <!-- Main Grid Container -->
    <div class="relative z-10 grid-16x9">
        <!-- Dynamic Island -->
        <div class="dynamic-island rounded-full flex items-center justify-center px-6">
            <div class="flex items-center space-x-4">
                <div class="w-3 h-3 bg-apple-green rounded-full animate-pulse"></div>
                <span class="text-sm font-medium">导航中 - 前往公司 · 预计 25 分钟</span>
                <div class="text-xs text-gray-300">8:15 AM</div>
            </div>
        </div>

        <!-- VPA Interaction Panel -->
        <div class="vpa-panel glass-card rounded-3xl p-6 hover-scale">
            <div class="h-full flex flex-col">
                <!-- VPA Avatar (No Border, No Background) -->
                <div class="flex-1 flex items-center justify-center mb-4">
                    <img src="vpn1.gif" alt="VPA Avatar" class="w-24 h-24 object-contain">
                </div>
                
                <!-- VPA Greeting -->
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-semibold mb-2">你好，我是小绿</h2>
                    <p class="text-gray-300 text-lg">今天想去哪里？</p>
                </div>
                
                <!-- Action Buttons -->
                <div class="space-y-4">
                    <button class="w-full bg-apple-blue hover:bg-blue-600 text-white font-semibold py-4 px-6 rounded-2xl transition-colors duration-200">
                        生成通勤桌面
                    </button>
                    <button class="w-full bg-apple-green hover:bg-green-600 text-white font-semibold py-4 px-6 rounded-2xl transition-colors duration-200">
                        播放音乐
                    </button>
                </div>
            </div>
        </div>

        <!-- Weather Card (Standard Card) -->
        <div class="standard-card glass-card rounded-3xl p-6 hover-scale" style="grid-column: 9 / 17; grid-row: 2 / 5;">
            <div class="h-full flex flex-col justify-between">
                <div class="flex items-center justify-between mb-4">
                    <div class="text-5xl">☀️</div>
                    <div class="text-right">
                        <div class="text-3xl font-light">28°</div>
                        <div class="text-gray-300">深圳</div>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-300">空气质量</span>
                        <span class="text-apple-green font-semibold">优</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">湿度</span>
                        <span>65%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Music Control Card (Large Card) -->
        <div class="large-card glass-card rounded-3xl p-6 hover-scale" style="grid-column: 9 / 17; grid-row: 5 / 10;">
            <div class="h-full flex flex-col">
                <!-- Album Art -->
                <div class="w-full h-40 mb-6 flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                         alt="Album Art" class="w-full h-full object-cover rounded-2xl">
                </div>
                
                <!-- Song Info -->
                <div class="text-center mb-4">
                    <h3 class="text-lg font-semibold mb-1">星辰大海</h3>
                    <p class="text-gray-300 text-sm">黄霄雲</p>
                </div>
                
                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="w-full bg-gray-600 rounded-full h-1">
                        <div class="bg-white h-1 rounded-full" style="width: 45%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-400 mt-1">
                        <span>1:23</span>
                        <span>3:45</span>
                    </div>
                </div>
                
                <!-- Controls -->
                <div class="flex items-center justify-center space-x-6">
                    <button class="text-xl hover:text-apple-blue transition-colors">
                        ⏮
                    </button>
                    <button class="text-3xl hover:text-apple-blue transition-colors bg-white bg-opacity-20 rounded-full w-12 h-12 flex items-center justify-center">
                        ▶️
                    </button>
                    <button class="text-xl hover:text-apple-blue transition-colors">
                        ⏭
                    </button>
                    <button class="text-lg hover:text-apple-blue transition-colors">
                        🔊
                    </button>
                </div>
            </div>
        </div>



        <!-- Quick Action Cards -->
        <div class="quick-action glass-card rounded-2xl p-4 hover-scale" style="grid-column: 1 / 9; grid-row: 8 / 10;">
            <div class="h-full flex flex-col justify-center space-y-2">
                <h4 class="text-sm font-semibold text-center mb-2">快捷指令</h4>
                <div class="grid grid-cols-2 gap-2">
                    <button class="bg-apple-blue bg-opacity-30 hover:bg-opacity-50 rounded-xl p-2 text-xs transition-colors">
                        导航回家
                    </button>
                    <button class="bg-apple-green bg-opacity-30 hover:bg-opacity-50 rounded-xl p-2 text-xs transition-colors">
                        开空调
                    </button>
                    <button class="bg-apple-orange bg-opacity-30 hover:bg-opacity-50 rounded-xl p-2 text-xs transition-colors">
                        播放音乐
                    </button>
                    <button class="bg-apple-red bg-opacity-30 hover:bg-opacity-50 rounded-xl p-2 text-xs transition-colors">
                        打电话
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Subtle Animations -->
    <script>
        // Add subtle breathing animation to VPA avatars
        const vpaElements = document.querySelectorAll('img[alt*="VPA"]');
        vpaElements.forEach(element => {
            element.style.animation = 'breathe 3s ease-in-out infinite';
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes breathe {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
        `;
        document.head.appendChild(style);

        // Add click interactions
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>