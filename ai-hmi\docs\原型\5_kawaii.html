<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI - Kawaii Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        body {
            overflow: hidden;
            font-family: 'Fredoka One', cursive;
            background-color: #fff0f5; /* Lavender Blush */
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 16px;
            width: 100vw;
            height: 100vh;
            padding: 16px;
            box-sizing: border-box;
            background-image: url('https://images.unsplash.com/photo-1549492423-400259a5e5a4?q=80&w=2574&auto=format&fit=crop');
            background-size: cover;
            background-position: center;
        }
        .card {
            background: white;
            border-radius: 24px;
            border: 3px solid #ffc0cb; /* Pink */
            box-shadow: 0 5px 0 #e7a5b1;
            transition: all 0.2s ease-in-out;
        }
        .card:hover {
            box-shadow: 0 8px 0 #e7a5b1;
        }
        .vpa-container { grid-column: span 2; grid-row: span 4; animation: bounce 2s infinite; }
        .island-container { grid-column: 3 / span 4; grid-row: 1; display: flex; align-items: center; justify-content: center; }
        .small-card-container { grid-column: 3 / span 4; grid-row: 2; display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; }
        .large-card-container { grid-column: 7 / span 2; grid-row: span 4; }
        .temp-interaction-container { position: absolute; bottom: 40px; left: 50%; transform: translateX(-50%); z-index: 50; }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap" rel="stylesheet">
</head>
<body class="text-gray-700">
    <div class="grid-container">
        <!-- VPA 数字人 -->
        <div class="vpa-container card flex flex-col items-center justify-center p-6">
            <img src="https://images.unsplash.com/photo-1598191492319-70a34a419952?q=80&w=2487&auto=format&fit=crop" alt="VPA Avatar" class="w-36 h-36 rounded-full border-4 border-pink-300 shadow-lg mb-5 object-cover">
            <h2 class="text-2xl font-bold text-pink-500">Mochi</h2>
            <p class="text-gray-600 text-center mt-2">Let's have a super fun day!</p>
        </div>

        <!-- 灵动岛 -->
        <div class="island-container card p-3">
            <div class="flex items-center space-x-4">
                <span class="text-4xl">💖</span>
                <div>
                    <p class="font-semibold text-pink-500">New Message!</p>
                    <p class="text-sm text-gray-500">From: Bestie ✨</p>
                </div>
            </div>
        </div>

        <!-- 横向小卡片 -->
        <div class="small-card-container">
            <div class="card p-5 flex flex-col justify-center items-center">
                <h3 class="font-bold text-lg text-purple-500 mb-2">My Stickers</h3>
                <div class="text-5xl"><span>🐱</span><span>🦄</span><span>⭐</span></div>
            </div>
            <div class="card p-5 flex flex-col justify-center items-center">
                <h3 class="font-bold text-lg text-green-500 mb-2">Snack Time!</h3>
                <div class="text-5xl"><span>🍰</span><span>🍓</span></div>
            </div>
        </div>

        <!-- 纵向大卡片 -->
        <div class="large-card-container card p-5 flex flex-col">
            <h3 class="font-bold text-lg text-blue-500 mb-3">Photo Album</h3>
            <div class="flex-grow grid grid-cols-2 gap-2">
                <img src="https://images.unsplash.com/photo-1529778873920-4da4926a72c2?q=80&w=2536&auto=format&fit=crop" class="rounded-lg object-cover w-full h-full border-2 border-yellow-300">
                <img src="https://images.unsplash.com/photo-1518791841217-8f162f1e1131?q=80&w=2670&auto=format&fit=crop" class="rounded-lg object-cover w-full h-full border-2 border-green-300">
                <img src="https://images.unsplash.com/photo-1513284916262-35ab7a4a5f62?q=80&w=2574&auto=format&fit=crop" class="rounded-lg object-cover w-full h-full border-2 border-blue-300">
                <img src="https://images.unsplash.com/photo-1561948955-570b270e7c36?q=80&w=2574&auto=format&fit=crop" class="rounded-lg object-cover w-full h-full border-2 border-red-300">
            </div>
        </div>

        <!-- 临时交互组件 -->
        <div class="temp-interaction-container card p-4 flex items-center space-x-4">
            <p class="font-semibold text-purple-500">Play a game?</p>
            <button class="px-6 py-2 rounded-full text-white bg-green-400 hover:bg-green-500 transform hover:scale-110 transition-transform">Yes!</button>
            <button class="px-6 py-2 rounded-full text-gray-600 bg-gray-200 hover:bg-gray-300">Maybe later</button>
        </div>

        <div style="grid-column: 3 / span 4; grid-row: 3 / span 2;"></div>

    </div>
</body>
</html>