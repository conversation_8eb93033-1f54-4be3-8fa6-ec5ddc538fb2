# 技术规格书 (Technical Specification)

- **文档版本:** 9.2 (Gemini Refactored)
- **状态:** 正式版

---

## 1. 核心技术栈

- **Vue.js**: 作为核心框架，负责构建用户界面。
- **Vite**: 作为构建工具，提供极速的开发服务器和优化的打包。
- **Tailwind CSS 4.1**: 作为核心UI样式框架，采用utility-first（工具类优先）的方法构建界面，确保样式的一致性、可维护性和原子化。
- **Pinia**: 用于状态管理，作为存储“桌面渲染计划”的单一事实来源。
- **GSAP (GreenSock Animation Platform)**: 作为首选动画库，用于实现所有高级的、精确控制的场景过渡效果。

---

## 2. 架构与实现

### 2.1. 总体架构

我们将采用基于Vue.js和Tailwind CSS的模块化前端架构。

```
/src
├── assets/              # 静态资源 (图片, 字体等)
├── components/          # Vue组件 (遵循原子化设计)
│   └── cards/           # 原子化的卡片组件
├── store/               # Pinia状态管理
├── services/            # 服务层 (AI模拟器, transitionManager)
├── composables/         # Vue组合式函数 (如useTheme)
├── styles/              # 全局样式
│   └── main.css         # Tailwind CSS指令和全局基础样式
├── themes/              # 原子化的主题定义 (用于Tailwind配置)
├── App.vue              # 根组件
├── main.js              # 应用入口
└── tailwind.config.js   # Tailwind CSS 配置文件 (核心)
└── postcss.config.js    # PostCSS 配置文件
```

### 2.2. 场景过渡实现：双场景容器技术

(此部分保持不变)

### 2.3. 统一主题与样式实现 (基于Tailwind CSS)

- **核心思想**: 设计系统（颜色、间距、字体、阴影等）在 `tailwind.config.js` 中进行集中定义。UI组件通过组合工具类来构建，而不是编写独立的CSS。
- **实现方式**: 
  1.  **主题定义**: 在 `src/themes/` 目录下定义不同的主题对象（如 `serenity.js`, `cyberpunk.js`），每个对象都导出一套符合Tailwind配置格式的颜色、字体等变量。
  2.  **动态主题切换**: `useTheme.js` 组合式函数将不再直接操作CSS变量。它的核心职责是：
      *   在 `<html>` 根元素上切换一个class，例如 `theme-serenity` 或 `theme-cyberpunk`。
      *   `tailwind.config.js` 将配置为使用 `class` 策略来响应这些主题的变化。
  3.  **样式应用**: 组件直接在模板中使用Tailwind的工具类，如 `bg-background text-text-primary rounded-lg`。

### 2.4. 原子化与可扩展性设计 (Atomization & Extensibility by Design)

(此部分原则保持不变，但实现将基于Tailwind)

#### 2.4.1. 主题的原子化 (Atomic Themes)

- **实现**: 每个主题都是一个独立的JS模块，导出一部分Tailwind配置。通过切换根元素的class来激活不同的主题配置。
- **扩展方式**: 在 `src/themes/presets/` 目录下新增一个 `new_theme.js` 文件，并在 `tailwind.config.js` 中引入即可。

#### 2.4.2. 过渡动画的原子化 (Atomic Transitions)

(此部分保持不变，GSAP与Tailwind可以良好协作)

#### 2.4.3. 卡片组件的原子化 (Atomic Components)

- **实现**: 每个卡片都是一个自包含的Vue单文件组件。其样式完全由Tailwind工具类在模板中定义，实现了样式和结构的内聚。
- **扩展方式**: 新增的 `NewWidgetCard.vue` 组件将直接使用项目已定义的Tailwind工具类来构建其外观，无需编写任何新的CSS。

---

## 3. 场景过渡特效库

(此部分保持不变)

---

## 4. 第三方库集成策略

### 4.1. 样式: Tailwind CSS
- **用途**: 作为项目唯一的样式解决方案，负责从布局、颜色到排版的全部视觉呈现。
- **集成**: 通过Vite和PostCSS进行集成。开发者在 `tailwind.config.js` 中扩展基础设计系统（如添加“玻璃拟态”效果的插件或工具类），并在 `src/styles/main.css` 中引入Tailwind的核心指令。

### 4.2. 动画: GSAP
(此部分保持不变)

### 4.3. 图标: Iconify
(此部分保持不变)

---

## 5. 关键代码示例

### 5.1. Tailwind CSS 配置示例 (`tailwind.config.js`)

```javascript
// tailwind.config.js
const plugin = require('tailwindcss/plugin')

// 导入预设主题
const serenityTheme = require('./src/themes/presets/serenity.js');
const cyberpunkTheme = require('./src/themes/presets/cyberpunk.js');

module.exports = {
  // 启用JIT模式，获得极速编译体验
  mode: 'jit',
  // 指定需要扫描的文件路径
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
  ],
  // 启用class策略进行暗黑/主题切换
  darkMode: 'class',
  theme: {
    extend: {
      // 在这里扩展基础主题
      colors: {
        // 定义基础颜色，可被主题覆盖
        'primary': 'var(--color-primary)',
        'background': 'var(--color-background)',
        'text-primary': 'var(--color-text-primary)',
      },
      // 扩展玻璃拟态效果
      backdropBlur: {
        'glass': '12px',
      },
    },
  },
  plugins: [
    // 注册主题
    plugin(function({ addBase, theme }) {
      addBase({
        ':root': serenityTheme, // 默认主题
        '.theme-cyberpunk': cyberpunkTheme,
        // 未来可在这里添加更多主题
      })
    }),
    // 添加一个自定义的玻璃拟态插件
    plugin(function({ addUtilities }) {
      addUtilities({
        '.glass-card': {
          'background-color': 'rgba(255, 255, 255, 0.1)',
          'backdrop-filter': 'blur(12px)',
          'border': '1px solid rgba(255, 255, 255, 0.2)',
        }
      })
    })
  ],
}
```

### 5.2. GSAP过渡管理器 (伪代码)

(此部分保持不变)

### 5.3. Pinia Store (伪代码)

(此部分保持不变)

---

## 6. 新增技术模块

### 6.1. 智能语音交互技术栈

#### 6.1.1. 语音识别与处理
```javascript
// 语音识别配置
const speechConfig = {
  engine: 'WebSpeech API + 百度语音',
  languages: ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'],
  realTimeProcessing: true,
  noiseReduction: true,
  wakeWordDetection: '小智助手'
}

// 多语言支持组件
class MultiLanguageVoice {
  constructor() {
    this.currentLanguage = 'zh-CN'
    this.supportedLanguages = speechConfig.languages
  }
  
  switchLanguage(lang) {
    this.currentLanguage = lang
    this.updateVoiceEngine(lang)
  }
}
```

#### 6.1.2. 语音状态可视化
```css
/* 语音波形动画 */
.voice-wave {
  @apply flex items-center gap-1;
}

.voice-wave-bar {
  @apply w-1 bg-blue-500 rounded-full transition-all duration-150;
  animation: voice-pulse 1.5s ease-in-out infinite;
}

@keyframes voice-pulse {
  0%, 100% { height: 4px; }
  50% { height: 16px; }
}
```

### ADAS集成技术架构

#### 车辆数据接口
```javascript
// ADAS数据模型
class AdasDataModel {
  constructor() {
    this.adaptiveCruise = {
      enabled: false,
      currentSpeed: 0,
      targetSpeed: 120,
      followDistance: 'medium'
    }
    
    this.laneKeeping = {
      enabled: false,
      confidence: 0.95,
      warnings: []
    }
    
    this.blindSpotMonitoring = {
      leftSide: false,
      rightSide: false,
      rearApproaching: false
    }
  }
  
  updateFromCanBus(data) {
    // 从CAN总线更新ADAS状态
    Object.assign(this, data)
    this.notifyUI()
  }
}
```

#### 安全预警系统
```javascript
// 安全预警管理器
class SafetyWarningManager {
  constructor() {
    this.warningLevels = {
      INFO: { color: 'blue', priority: 1 },
      WARNING: { color: 'yellow', priority: 2 },
      CRITICAL: { color: 'red', priority: 3 }
    }
  }
  
  triggerWarning(type, message, level = 'WARNING') {
    const warning = {
      id: Date.now(),
      type,
      message,
      level,
      timestamp: new Date(),
      dismissed: false
    }
    
    this.displayWarning(warning)
    this.logWarning(warning)
  }
}
```

### 车联网服务架构

#### 充电站服务集成
```javascript
// 充电站API集成
class ChargingStationService {
  constructor() {
    this.apiEndpoint = 'https://api.charging-network.com'
    this.providers = ['特来电', '星星充电', '国家电网']
  }
  
  async findNearbyStations(location, radius = 5) {
    const response = await fetch(`${this.apiEndpoint}/stations/nearby`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ location, radius })
    })
    
    return response.json()
  }
  
  async reserveCharging(stationId, timeSlot) {
    // 预约充电桩
    return await this.apiCall('/reserve', { stationId, timeSlot })
  }
}
```

#### 远程车辆控制
```javascript
// 远程控制服务
class RemoteVehicleControl {
  constructor() {
    this.connection = new WebSocket('wss://vehicle-control.api')
    this.commands = {
      CLIMATE: 'climate_control',
      WINDOWS: 'window_control',
      DOORS: 'door_control',
      LIGHTS: 'light_control'
    }
  }
  
  async sendCommand(command, params) {
    const message = {
      command,
      params,
      timestamp: Date.now(),
      vehicleId: this.vehicleId
    }
    
    return new Promise((resolve, reject) => {
      this.connection.send(JSON.stringify(message))
      // 处理响应...
    })
  }
}
```

### 个性化体验技术实现

#### 用户识别系统
```javascript
// 多模态用户识别
class UserIdentificationSystem {
  constructor() {
    this.methods = {
      voice: new VoicePrintRecognition(),
      face: new FaceRecognition(),
      phone: new PhoneProximityDetection(),
      manual: new ManualSelection()
    }
  }
  
  async identifyUser() {
    const results = await Promise.allSettled([
      this.methods.voice.identify(),
      this.methods.face.identify(),
      this.methods.phone.identify()
    ])
    
    return this.consolidateResults(results)
  }
  
  loadUserProfile(userId) {
    return {
      preferences: this.getUserPreferences(userId),
      history: this.getUserHistory(userId),
      recommendations: this.generateRecommendations(userId)
    }
  }
}
```

#### 情绪感知技术
```javascript
// 情绪检测引擎
class EmotionDetectionEngine {
  constructor() {
    this.sensors = {
      voice: new VoiceEmotionAnalyzer(),
      face: new FacialExpressionAnalyzer(),
      driving: new DrivingBehaviorAnalyzer()
    }
  }
  
  async detectEmotion() {
    const voiceEmotion = await this.sensors.voice.analyze()
    const faceEmotion = await this.sensors.face.analyze()
    const drivingEmotion = await this.sensors.driving.analyze()
    
    return this.fuseEmotionData({
      voice: voiceEmotion,
      face: faceEmotion,
      driving: drivingEmotion
    })
  }
  
  generateRecommendations(emotion) {
    const recommendations = {
      music: this.getMusicRecommendations(emotion),
      lighting: this.getLightingRecommendations(emotion),
      climate: this.getClimateRecommendations(emotion)
    }
    
    return recommendations
  }
}
```

### 安全隐私技术架构

#### 访客模式实现
```javascript
// 访客模式管理器
class GuestModeManager {
  constructor() {
    this.guestSession = null
    this.allowedFeatures = [
      'navigation',
      'music',
      'climate',
      'emergency_contact'
    ]
  }
  
  activateGuestMode(pin) {
    if (this.validatePin(pin)) {
      this.guestSession = {
        id: this.generateSessionId(),
        startTime: Date.now(),
        restrictions: this.getGuestRestrictions()
      }
      
      this.hidePersonalData()
      this.limitFeatureAccess()
      return true
    }
    return false
  }
  
  deactivateGuestMode() {
    this.clearTemporaryData()
    this.restorePersonalData()
    this.guestSession = null
  }
}
```

#### 数据加密与隐私保护
```javascript
// 隐私保护管理器
class PrivacyProtectionManager {
  constructor() {
    this.encryptionKey = this.generateEncryptionKey()
    this.dataCategories = {
      location: { retention: '7days', encryption: true },
      voice: { retention: '1day', localOnly: true },
      biometric: { retention: 'session', encryption: true },
      usage: { retention: '30days', anonymized: true }
    }
  }
  
  encryptSensitiveData(data, category) {
    const config = this.dataCategories[category]
    if (config.encryption) {
      return this.encrypt(data, this.encryptionKey)
    }
    return data
  }
  
  scheduleDataCleanup() {
    Object.entries(this.dataCategories).forEach(([category, config]) => {
      this.scheduleCleanup(category, config.retention)
    })
  }
}
```

### 性能优化策略

#### 组件懒加载
```javascript
// 动态组件加载
const LazyComponents = {
  VoiceInterface: () => import('@/components/VoiceInterface.vue'),
  AdasPanel: () => import('@/components/AdasPanel.vue'),
  ChargingStations: () => import('@/components/ChargingStations.vue'),
  EmotionDetector: () => import('@/components/EmotionDetector.vue')
}

// 基于场景的组件预加载
class ComponentPreloader {
  constructor() {
    this.preloadStrategies = {
      driving: ['AdasPanel', 'Navigation'],
      charging: ['ChargingStations', 'Entertainment'],
      parking: ['RemoteControl', 'Security']
    }
  }
  
  preloadForScenario(scenario) {
    const components = this.preloadStrategies[scenario] || []
    components.forEach(component => {
      LazyComponents[component]()
    })
  }
}
```

#### 数据缓存策略
```javascript
// 智能缓存管理
class IntelligentCacheManager {
  constructor() {
    this.cache = new Map()
    this.cacheStrategies = {
      userPreferences: { ttl: 86400000, persistent: true },
      locationData: { ttl: 300000, persistent: false },
      voiceCommands: { ttl: 3600000, persistent: true },
      adasStatus: { ttl: 5000, persistent: false }
    }
  }
  
  set(key, value, strategy = 'default') {
    const config = this.cacheStrategies[strategy]
    const item = {
      value,
      timestamp: Date.now(),
      ttl: config.ttl,
      persistent: config.persistent
    }
    
    this.cache.set(key, item)
    
    if (config.persistent) {
      localStorage.setItem(`cache_${key}`, JSON.stringify(item))
    }
  }
}
```